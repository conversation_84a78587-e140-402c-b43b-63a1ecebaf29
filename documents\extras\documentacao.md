# Documentação Modelo Preditivo - Inteli

## Infopepper

### Chillibinos

#### Integrantes

- <PERSON> - [ [Github](https://github.com/GabriellReisss) | [LinkedIn](https://www.linkedin.com/in/gab<PERSON>-reis-07170727b/) ]

- <PERSON> - [ [Github](https://github.com/Zanette00) | [LinkedIn](https://www.linkedin.com/in/gabriel-c-zanette/) ]

- <PERSON> - [ [Github](https://github.com/JoaoAidar) | [LinkedIn](https://www.linkedin.com/in/jo%C3%A3o-aidar-689097246/) ]

- <PERSON> - [ [Github](https://github.com/leormsvieira) | [LinkedIn](https://www.linkedin.com/in/leonardoramosvieira/) ]

- <PERSON> - [ [Github](https://github.com/J05UE-l) | [LinkedIn](https://www.linkedin.com/in/rafael-josue/) ]

- <PERSON> - [ [Github](https://github.com/V0no) | [LinkedIn](https://www.linkedin.com/in/samuel-vono) ]

- Yan Dimitri Kruziski - [ [Github](https://github.com/yankruziski) | [LinkedIn](https://www.linkedin.com/in/yan-dimitri-kruziski/) ]

## Sumário

[1. Introdução](#c1)

[2. Objetivos e Justificativa](#c2)

[3. Metodologia](#c3)

[4. Desenvolvimento e Resultados](#c4)

[5. Conclusões e Recomendações](#c5)

[6. Referências](#c6)

[Anexos](#attachments)


## <a name="c1"></a>1. Introdução

&nbsp;&nbsp;Este projeto conta com a parceria da Chilli Beans, consolidada como a maior rede especializada em óculos e acessórios da América Latina. A empresa, que atua no setor de varejo de moda, foi fundada no final dos anos 90 e expandiu para mais de 900 pontos de venda espalhados pelo Brasil além de outros 21 países, como Portugal e Estados Unidos. Seu posicionamento de mercado é evidenciado por lojas conceito e pela operação de três identidades de marca distintas: Chilli Beans, Óticas Chilli Beans e Eco Chilli.

&nbsp;&nbsp;O problema central do projeto se desdobra em três desafios: primeiramente, a empresa encontra dificuldade das lojas de rua da Ótica Chilli Beans em atrair fluxo de clientes e gerar conversão, por se tratar de um modelo novo para a imagem da marca, ainda não se consolidou na percepção do público, acostumado com a imagem tradicional da marca. O segundo desafio da marca é superar a dificuldade de se conectar com o público mais jovem e, com isso, atrair clientes dessa faixa etária. Por fim, o terceiro problema é o baixo reconhecimento da marca na categoria de óculos de grau, sendo preciso compreender o comportamento do consumidor para tornar a Chilli Beans relevante e reconhecida neste segmento.

## <a name="c2"></a>2. Objetivos e Justificativa

### 2.1 Objetivos

```
Descreva resumidamente os objetivos gerais e específicos do seu parceiro de negócios.

Remova este bloco ao final
```

### 2.2 Proposta de solução

```
Descreva resumidamente sua proposta de modelo preditivo e como esse modelo pretende resolver o problema, atendendo os objetivos.

Remova este bloco ao final
```

### 2.3 Justificativa

```
Faça uma breve defesa de sua proposta de solução, escreva sobre seus potenciais, seus benefícios e como ela se diferencia.

Remova este bloco ao final
```

## <a name="c3"></a>3. Metodologia

Nesta seção, descrevemos a abordagem metodológica adotada com fundamentação teórica e justificativas técnicas, considerando tanto modelagem supervisionada quanto não‑supervisionada, em alinhamento à literatura e ao contexto de negócio.

3.1. Delineamento geral e etapas
- Coleta/preparação: consolidação do dataset, tratamento de ausentes, outliers e padronização conforme distribuição (vide Seções 1 e 2 do pipeline).
- EDA: inspeções uni/multivariadas; correlações (Pearson/Spearman/Kendall); estudos temporais; controle de confundidores.
- Feature Engineering: seleção guiada por relevância e colinearidade; transformações (log1p/Box‑Cox) e escalonamento (z‑score/robusto/min‑max) conforme testes de normalidade.
- Modelagem híbrida: supervisão (Random Forest) para previsão de receita/potencial; não‑supervisão (K‑Means) para segmentação e ações de marketing.
- Validação/interpretação: validação cruzada (5‑fold) com métricas apropriadas; interpretação via importâncias, PCA e análises por grupos.

3.2. Justificativas técnicas
- Exclusão sistemática de IDs nas análises/modelagem (mantidos apenas para cardinalidade/joins), reduzindo viés e superajuste.
- Random Forest: robustez a não‑linearidades/outliers e boa performance em dados tabulares.
- K‑Means: custo computacional baixo e interpretabilidade; seleção de K por Silhouette; PCA para projeção e diagnóstico.
- Classificação para propensão (XGBoost/GB): adequado para relações não lineares; métricas ROC‑AUC/AP/F1.

3.3. Métricas e procedimentos
- Regressão: RMSE, MAE e R² (CV 5‑fold) para erro médio e variância explicada.
- Clustering: Silhouette, Inertia e Davies‑Bouldin para qualidade de segmentação.
- Classificação: ROC‑AUC, Average Precision e F1 com validação estratificada.

3.4. Reprodutibilidade e ambiente
- Seeds fixos (42) para procedimentos estocásticos.
- Notebooks executáveis fim‑a‑fim, com artefatos em reports/2025‑08‑15/ e modelos em models/.
- Notebook Google Colab (notebooks/chilli_beans_analysis_colab.ipynb) com célula de instalação de dependências.

## <a name="c4"></a>4. Desenvolvimento e Resultados

### 4.1. Compreensão do Problema

#### 4.1.1. Contexto da indústria
&nbsp;&nbsp;A Chilli Beans é a maior rede especializada em óculos e acessórios da América Latina, reconhecida por seu posicionamento ousado, coleções temáticas e comunicação irreverente. Desde sua fundação no fim dos anos 1990, a marca construiu um ecossistema único no varejo óptico, combinando velocidade de lançamento de produtos com forte apelo cultural. Com mais de 900 pontos de venda distribuídos entre lojas próprias, franquias e quiosques, a empresa atua em diferentes mercados e formatos, o que exige adaptabilidade constante.

&nbsp;&nbsp;O modelo franqueador é um dos pilares de crescimento da marca. Ele garante capilaridade e presença em regiões diversas, mas também impõe desafios: cada unidade opera em um contexto local distinto, com variações no perfil socioeconômico, comportamento de consumo e concorrência. Nesse cenário, decisões estratégicas baseadas apenas em intuição ou histórico limitado tendem a gerar riscos de baixa performance.

&nbsp;&nbsp;É nesse ponto que os modelos preditivos se tornam diferenciais competitivos. Ao integrar dados internos (vendas, mix de produtos, conversão por canal) com informações externas (renda média da região, fluxo de pessoas, presença de concorrentes, comportamento sazonal), a Chilli Beans pode prever o potencial de cada loja ou região, identificar oportunidades e antecipar ameaças.

&nbsp;&nbsp;Essa capacidade de previsão permite não só otimizar a escolha de novos pontos de venda, mas também adaptar estratégias de marketing, precificação e portfólio para cada contexto. No ambiente franqueado, esses insights orientam ações personalizadas, aumentando a conversão e fortalecendo o desempenho da rede como um todo.

&nbsp;&nbsp;Ao aliar sua criatividade e identidade marcante à inteligência analítica, a Chilli Beans não apenas mantém sua relevância no mercado óptico, mas também posiciona-se na vanguarda do varejo orientado por dados, transformando previsões em resultados concretos para franqueados e consumidores.

#### 5 forças de Porter

![5 forças de porter](/assets/5forcas1.png)

![5 forças de porter](/assets/5forcas2.png)

##### 1. Poder de barganha dos fornecedores – Médio baixo
&nbsp;&nbsp;A Chilli Beans mantém parcerias de longo prazo com fornecedores estratégicos, garantindo alinhamento de qualidade e prazos. No entanto, devido à sua escala e posicionamento consolidado, a empresa teria capacidade de negociar novos acordos ou trocar de fornecedor se necessário.

##### 2. Ameaça de novos entrantes – Médio baixo
&nbsp;&nbsp;Entrar no setor requer investimento significativo em marca, design, canais de distribuição e tecnologia ótica. Além disso, é necessário conquistar a confiança do público, algo que demanda tempo e alto custo, tornando a barreira de entrada considerável.

##### 3. Ameaça de produtos substitutos – Alta
&nbsp;&nbsp;Clientes podem substituir os óculos da marca por opções genéricas, importados de baixo custo ou produtos de marcas locais, priorizando economia em detrimento da qualidade ou apelo de moda. A ameaça cresce com a popularização do e-commerce e das compras internacionais.

##### 4. Poder de barganha do consumidor – Médio a Alto
&nbsp;&nbsp;O mercado de moda e ótica oferece ampla diversidade de opções. Consumidores são sensíveis a preço, mas também valorizam a singularidade de peças exclusivas, colaborações com artistas e design diferenciado. A fidelidade é conquistada mais por experiência e estilo do que por preço.

##### 5. Rivalidade entre concorrentes – Alta
&nbsp;&nbsp;Concorrência intensa com grandes marcas de moda, redes de óticas tradicionais e plataformas online que distribuem peças de moda e acessórios. Há disputa acirrada tanto pelo preço quanto pelo design e posicionamento de marca.
&nbsp;&nbsp;Principais concorrentes: Ray-Ban, Oakley, Zerezes, Evoke, HB (Hot Buttered), Óticas Carol, Óticas Diniz, Mercadão dos Óculos, além de marketplaces como Amazon, Shein e Mercado Livre.

#### 4.1.2. Análise SWOT

&nbsp;&nbsp;Além disso, a análise se estende ao ambiente externo para mapear as oportunidades, que são as tendências de mercado favoráveis que a empresa pode e deve explorar, e as ameaças, representadas pelos obstáculos e riscos que exigem um plano de defesa bem estruturado. Ao conectar esses quatro pilares, a análise contribui para o desenvolvimento de estratégias mais assertivas e alinhadas ao contexto analisado. Dessa forma, confira a Análise SWOT da Chilli Beans elaborada por nossa equipe a seguir:

<div align="center">

<sub>Figura x: Análise SWOT da Chilli Beans</sub>

<img src="../../assets/analise-swot.png" style=width:100%>

<sub>Fonte: Autoria própria (2025)</sub>

</div>

&nbsp;&nbsp;A Chilli Beans consolida sua liderança no mercado por meio de uma identidade de marca forte e um modelo de negócio fast fashion, sustentados por uma vasta rede de vendas e um marketing agressivo. Contudo, a complexidade na gestão de sua rede de franquias e vulnerabilidades em sua cadeia de suprimentos surgem como fraquezas internas que podem frear seu ritmo de crescimento. A expansão internacional e a exploração de novos nichos representam claras oportunidades para alavancar ainda mais a marca.

&nbsp;&nbsp;Essa diversificação, aliás, já é uma realidade estratégica, com a Chilli Beans estendendo sua identidade "apimentada" para um ecossistema de produtos que inclui desde relógios e acessórios de moda até um energético próprio, transformando-se de uma ótica para uma grife de estilo de vida. No entanto, a concorrência intensa, a pirataria e as rápidas mudanças nas tendências de moda são ameaças constantes. Para manter sua posição dominante e se proteger dos riscos, a Chilli Beans precisa otimizar sua gestão interna e sua logística, garantindo que sua poderosa.

#### 4.1.3. Planejamento Geral da Solução

**a) Quais os dados disponíveis?**

&nbsp;&nbsp;Os dados disponíveis são fornecidos pela Chilli Beans, conforme detalhado na planilha de entrada. Eles incluem:

* **Dados de Venda:** Identificadores de vendedor, cliente, produto e loja; data da venda, número do pedido, tipo de operação (venda ou devolução), quantidade e valor total da venda, desconto aplicado e preço de varejo.
* **Dados do Cliente:** ID do cliente, cidade, estado, sexo, data de nascimento e data de cadastro.
* **Dados do Produto:** ID do produto, categoria, canal da marca, referência e nome do produto, além da coleção à qual ele pertence.
* **Dados da Loja:** Código SAP da loja, descrição do nome da loja e tipo de PDV (Ponto de Venda), como "Rua", "quiosque", "shopping" e "Eco".


**b) Qual a solução proposta?**

&nbsp;&nbsp;A solução proposta é o **InfoPepper**, um sistema de análise preditiva. O projeto será entregue como um **MVP (Produto Mínimo Viável) em Jupyter Notebooks** interativos. A solução utiliza modelos de machine learning para resolver três problemáticas da Chilli Beans, cada uma endereçada por um notebook específico:

1.  **Análise Territorial (Notebook 1):** Um modelo preditivo que ranqueia regiões por potencial de sucesso para a abertura de lojas, com base em dados de vendas, dados demográficos e de concorrência.
2.  **Segmentação de Clientes (Notebook 2):** Um modelo de clustering que segmenta a base de clientes para campanhas de marketing mais eficientes, identificando o público ideal para cada canal da marca.
3.  **Identificação de Leads (Notebook 3):** Um modelo de lead scoring que identifica clientes com alta probabilidade de compra de lentes de grau, ajudando a impulsionar as vendas dessa categoria.


**c) Como a solução deverá ser utilizada?**

&nbsp;&nbsp;A solução é entregue como códigos e Jupyter Notebooks interativos, cada um direcionado a uma necessidade (território, segmentação, leads). O usuário importa a planilha de entrada, executa o notebook (pipeline pré‑definido) e obtém insights acionáveis — listas de leads qualificados, mapas interativos com scores de potencial e perfis de segmentos — para apoiar decisões de expansão, marketing e operação.


**d) Quais os benefícios trazidos pela solução proposta?**

&nbsp;&nbsp;A implementação do InfoPepper trará os seguintes benefícios estratégicos:

* **Decisões de Expansão Mais Assertivas:** Aumenta a taxa de sucesso de novas lojas ao identificar as regiões com maior potencial de crescimento.
* **Campanhas de Marketing Mais Eficientes:** Otimiza o investimento em marketing ao direcionar campanhas para segmentos de clientes com maior propensão de compra.
* **Aumento de Vendas da Categoria de Grau:** Impulsiona o crescimento de uma categoria-chave ao fornecer listas de clientes com alta probabilidade de compra de lentes de grau.
* **Otimização de Recursos:** Permite que a Chilli Beans concentre seus esforços em clientes e regiões que oferecem o maior potencial de retorno.


**e) Qual será o critério de sucesso?**

&nbsp;&nbsp;O sucesso do projeto será medido pelos resultados obtidos nas áreas de negócio, em linha com os seguintes critérios específicos:

* Aumentar as vendas de lentes entre os leads priorizados;
* Melhorar a performance e o crescimento das lojas formato Eco;
* Elevar a taxa de sucesso das novas aberturas recomendadas pelo modelo territorial
* Aumentar significativamente a taxa de conversão das campanhas direcionadas aos clusters identificados.

#### 4.1.4. Value Proposition Canvas

&nbsp;&nbsp;O Canvas de Proposta de Valor funciona como um mapa estratégico para conectar as características de um produto ou serviço aos anseios do cliente. Por meio dele, adquirimos uma visão clara sobre os desafios e as motivações do cliente, o que possibilita a criação de soluções com alto valor percebido. Consequentemente, a proposta de valor se torna uma declaração poderosa sobre nossos diferenciais, definindo nosso lugar no mercado e o impacto transformador de nossa oferta.

<div align="center">

<sub>Figura x: Canvas Proposta de Valor</sub>

<img src="../../assets/canvas-proposta-valor.jpg" style=width:100%>

<sub>Fonte: Autoria própria (2025)</sub>

</div>

&nbsp;&nbsp;A proposta de valor desenvolvida tem como foco atender diretamente às principais necessidades da liderança da Chilli Beans e, ao mesmo tempo, otimizar a expansão estratégica das lojas de rua. Dessa maneira, o perfil do cliente contempla os gestores e a equipe de estratégia da Chilli Beans. Sendo assim, mapeamos esse perfil nessas três frentes:

- **Ganhos esperados**, que destacam a busca por insights para uma expansão estratégica segura e a obtenção de resultados mensuráveis. O principal desejo é tomar decisões de crescimento baseadas em dados concretos, que levem ao aumento de visitas e à maior conversão em vendas nas lojas de rua.

- **Tarefas do cliente**, que giram em torno da necessidade de aumentar o fluxo e a conversão das Óticas de rua. Para isso, precisam identificar as regiões com maior potencial de sucesso , entender os fatores locais que impactam a performance e direcionar ações de marketing de forma mais eficaz.

- **Dores**, como a dificuldade em tomar decisões de expansão devido à "ausência de histórico robusto"  para este novo modelo de loja. Isso gera um alto grau de incerteza e o risco de investir em locais com baixo desempenho. Além disso, enfrentam o desafio de analisar de forma integrada fatores complexos como dados socioeconômicos e a presença da concorrência em cada região.

&nbsp;&nbsp;Para responder a essas demandas, desenvolvemos uma solução composta por três frentes:

- **Analgésicos**, que atuam no alívio imediato das dores identificadas, incluem um modelo preditivo que gera um score de potencial para cada região, reduzindo a incerteza. A solução também oferece um diagnóstico comparativo que permite identificar se o baixo desempenho de uma loja se deve à localização ou a fatores operacionais, direcionando ações corretivas.

- **Produtos e serviços**, representados por um notebook de análise. Essa ferramenta centraliza a análise de dados complexos em uma plataform intuitiva, que entrega um mapa de calor com o ranking de regiões e recomendações claras para os gestores.

- **Criadores de ganhos**, que agregam valor ao oferecer uma lista ranqueada de regiões com o maior potencial de sucesso para expansão. A solução também identifica os fatores-chave que influenciam a performance em cada território, fornecendo insights acionáveis que permitem um crescimento mais rápido e seguro.

&nbsp;&nbsp;Dessa forma, nosso principal objetivo é auxiliar a Chilli Beans a cumprir seu objetivo de aumentar o fluxo e a conversão das unidades de rua, fortalecendo sua estratégia de expansão com decisões baseadas em dados e gerando um crescimento mais eficiente e sustentável para a marca.

#### 4.1.5. Matriz de Riscos

&nbsp;&nbsp;A Matriz de Riscos e Oportunidades é como um mapa que ajuda a nos organizar com o que pode dar errado (ameaças) e o com que pode dar muito certo (oportunidades) no projeto. Trazendo assim para o projeto, a ideia de usá-la para a equipe se preparar para desafios conhecidos, como a qualidade dos dados ou a aceitação da ferramenta pela equipe. Ao mesmo tempo, que pode-se ver de olho para não deixar passar boas chances que apareçam, como a de replicar o modelo em outras áreas.

<div align="center">

<sub>Figura x: Matriz de Risco do Projeto</sub>

<img src="../../assets/matriz_de_riscos.png" style=width:100%>

<sub>Fonte: Autoria própria (2025)</sub>

</div>

##### R1: Qualidade ou falta de dados internos

&nbsp;&nbsp;Descrição da Ameaça: Dados de vendas das lojas são incompletos, inconsistentes entre sistemas ou com erros de registro, comprometendo a análise.

Impacto: Muito Alto
Nível: Alta
Plano de Ação: Realizar uma fase de avaliação da qualidade dos dados no início, criar scripts de limpeza e padronização e, se necessário, ajustar o escopo para as lojas com dados confiáveis.

##### R2: Modelo preditivo com baixa acurácia

&nbsp;&nbsp;Descrição da Ameaça: O modelo é desenvolvido, mas suas previsões não correspondem à realidade, gerando recomendações ineficazes.
Impacto: Muito Alto
Nível: Alta
Plano de Ação: Separar bases de treino, teste e validação, testar diferentes algoritmos e validar os resultados com a equipe de negócios em cada etapa.

##### R3: Baixa adoção pelos usuários finais

&nbsp;&nbsp;Descrição da Ameaça: O dashboard é entregue, mas os gestores (Ricardo, Marcos) o consideram complicado ou não confiam nos insights.

Impacto: Alto
Nível: Alta
Plano de Ação: Envolver os usuários finais desde o início no desenho do dashboard e realizar treinamentos práticos focados em como a ferramenta responde às suas dores.

##### R4: Indisponibilidade de dados externos

&nbsp;&nbsp;Descrição da Ameaça: Dificuldade ou alto custo para obter dados de mercado (fluxo, concorrência) que são essenciais para o modelo.

Impacto: Alto
Nível: Alta
Plano de Ação: Iniciar a prospecção de fornecedores o quanto antes e ter um "Plano B" utilizando dados públicos (IBGE) ou criando variáveis "proxy" (aproximações).

##### R5: Modelo "Caixa-Preta" (não explicável)

&nbsp;&nbsp;Descrição da Ameaça: O modelo funciona, mas não é possível explicar por que ele recomenda uma região, gerando desconfiança.

Impacto: Alto
Nível: Alta
Plano de Ação: Priorizar o uso de modelos interpretáveis (ex: Árvores de Decisão) e utilizar técnicas de "Explainable AI" (XAI) para traduzir as decisões do modelo.

##### R6: Resistência dos Franqueados

&nbsp;&nbsp;Descrição da Ameaça: As recomendações são vistas como imposições "de cima para baixo" que não consideram a realidade local.

Impacto: Moderado
Nível: Média
Plano de Ação: Criar um canal de feedback, apresentar os resultados como "sugestões estratégicas" e iniciar um piloto com franqueados parceiros para criar cases de sucesso.

##### O1: Replicabilidade do Modelo

&nbsp;&nbsp;Descrição da Oportunidade: O modelo para lojas de rua se mostra tão eficaz que pode ser rapidamente adaptado para outros formatos da Chilli Beans (quiosques, lojas Eco), gerando valor inesperado para o negócio.

Impacto: Muito Alto
Nível: Baixo
Plano de Ação: Explorar a oportunidade construindo o modelo de forma modular desde o início, pensando na sua futura adaptação e documentando o processo para facilitar a replicação.

##### O2: Engajamento dos Franqueados

&nbsp;&nbsp;Descrição da Oportunidade: Franqueados se tornam "campeões" do projeto, criando cases de sucesso que aceleram a adoção da cultura de dados por toda a rede, superando a meta de engajamento.

Impacto: Alto
Nível: Baixo
Plano de Ação: Melhorar a chance de isso acontecer identificando franqueados com perfil inovador para um projeto piloto e criando um programa de reconhecimento para os que melhor utilizarem a ferramenta.

##### O3: Descoberta de um "Golden Insight"

&nbsp;&nbsp;Descrição da Oportunidade: Durante a análise, descobre-se uma correlação não óbvia e muito forte (ex: um produto específico que atua como "porta de entrada" para clientes de alto valor), que pode reorientar a estratégia de marketing.

Impacto: Muito Alto
Nível: Baixo
Plano de Ação: Melhorar a chance de descoberta alocando tempo para análise exploratória livre e incentivando a equipe de BI a buscar padrões inesperados, além de apenas responder ao escopo inicial.

##### O4: Surgimento de Parcerias Estratégicas

&nbsp;&nbsp;Descrição da Oportunidade: Na busca por dados externos, identifica-se uma empresa (ex: um aplicativo de geolocalização, uma imobiliária comercial) com quem a Chilli Beans pode firmar uma parceria vantajosa para ambas.

Impacto: Moderado
Nível: Média
Plano de Ação: Melhorar a chance de surgimento mapeando potenciais parceiros durante a fase de coleta de dados e estando aberto a discutir modelos de negócio que vão além da simples compra de dados.



&nbsp;&nbsp;O importante é pensar nesta matriz como um guia que nos acompanhará durante o Projeto. O sucesso virá do equilíbrio: temos que nos dedicar a resolver os problemas mais sérios que apontamos e, ao mesmo tempo, correr atrás das boas oportunidades que mapeamos. A gente precisa olhar para ele de tempos em tempos, para ter certeza de que estamos no caminho certo e tomando as melhores decisões para a Chilli Beans.

#### 4.1.6. Personas

&nbsp;&nbsp;As personas a seguir representam perfis-chave de indivíduos envolvidos ou impactados pelo projeto preditivo da Chilli Beans. Elas foram desenvolvidas com o propósito de orientar o desenvolvimento do modelo preditivo, garantindo que as soluções propostas atendam tanto às necessidades estratégicas da empresa quanto às expectativas dos clientes. Cada persona reflete dores, necessidades e comportamentos específicos, ajudando a equipe a visualizar cenários reais de uso e a alinhar o modelo preditivo de forma mais eficaz aos objetivos de negócio e à experiência do consumidor.

#### Persona 1 – Perfil Corporativo Estratégico (Diretamente Afetada)

![Ficha Juliana](/assets/ficha_juliana.png)

###### Perfil:
&nbsp;&nbsp;Juliana, 45 anos, é diretora de Estratégia e Expansão na Chilli Beans. Ela atua no corporativo, tomando decisões estratégicas sobre onde abrir novas lojas, como segmentar o mercado e quais campanhas de marketing ou produtos priorizar. Com larga experiência em varejo de moda, Juliana é orientada a resultados e responde pela expansão sustentável da marca e pelo aumento de receita. Ela valoriza análises de alto nível e insights confiáveis que a auxiliem a alinhar iniciativas de expansão, CRM e marketing aos objetivos de crescimento da empresa.

###### Dores e Necessidades:
&nbsp;&nbsp;Decisões com Incerteza: Sente dificuldade em identificar, com base em dados, quais regiões ou canais têm maior potencial para novas lojas e campanhas. Ela precisa de insights preditivos claros para embasar decisões de expansão e evitar investimentos em iniciativas pouco promissoras.

###### Conhecer o Cliente Ideal:
&nbsp;&nbsp;Percebe lacunas no entendimento do público-alvo. Juliana necessita definir o perfil ideal de cliente – suas preferências, faixa etária, renda e hábitos – para direcionar estratégias de marketing mais assertivas e personalizar a comunicação, aumentando a taxa de conversão de novos clientes.

###### Pressão por Resultados:
  &nbsp;&nbsp;Sob pressão por crescimento contínuo, busca maneiras de otimizar campanhas e portfólio de produtos. Ela precisa de indicadores antecipados do mercado (por exemplo, quais campanhas terão mais impacto em cada cluster de consumidores) para alocar recursos de forma eficaz e justificar suas decisões junto à diretoria executiva.

###### Visão Integrada de Negócio:
  &nbsp;&nbsp;Enfrenta a dificuldade de conectar dados de diferentes frentes (expansão geográfica, vendas por categoria, desempenho de lojas) em uma visão única. Tem a necessidade de relatórios consolidados e estratégicos que facilitem o monitoramento do desempenho geral e apontem oportunidades ou riscos emergentes.

###### Comportamentos:
&nbsp;&nbsp;Juliana acompanha regularmente relatórios de vendas e tendências de mercado, participando de reuniões com equipes de marketing, produto e expansão. Toma decisões baseadas em dados disponíveis, mas muitas vezes de forma intuitiva quando faltam análises aprofundadas. Ela costuma planejar campanhas nacionais e iniciativas de expansão anual, ajustando o plano conforme resultados trimestrais. É adepta de ferramentas de BI para visualizar indicadores-chave, embora dependa da equipe de dados para interpretações mais complexas. Discute frequentemente com times operacionais para entender desafios nas lojas, mantendo uma visão macro do negócio sem perder de vista a realidade operacional.

###### Impacto do Modelo:
&nbsp;&nbsp;Com a implementação do modelo preditivo, Juliana passa a fundamentar suas estratégias em análises robustas. Decisões antes incertas tornam-se confiáveis – por exemplo, o modelo indica quais regiões possuem maior potencial de fluxo qualificado, orientando onde abrir a próxima ótica de rua ou intensificar marketing. Ela recebe perfis de clusters de clientes e previsão de conversão por segmento, o que permite personalizar campanhas e ofertas para cada público, aumentando a efetividade do CRM. Insights visuais como mapas de calor por região e perfil de cliente destacam oportunidades e facilitam sua comunicação com stakeholders.
Em resumo, o modelo dá a Juliana clareza e agilidade: agora suas propostas de expansão e iniciativas de produto são respaldadas por dados preditivos, reduzindo riscos e aumentando a confiança da diretoria nas decisões tomadas.

###### Conclusão:
&nbsp;&nbsp;Como decisora estratégica diretamente afetada pelo projeto, Juliana vê suas principais dores serem endereçadas. Ela ganha uma compreensão aprofundada do público ideal e dos fatores que impulsionam o desempenho das lojas, possibilitando-lhe traçar planos de expansão e marketing mais assertivos.
Com o suporte do modelo preditivo, Juliana conquista maior segurança para inovar – seja ao entrar em um novo mercado ou ao lançar uma campanha – sabendo que suas ações estão alinhadas a insights de dados. Isso resulta em melhores indicadores de crescimento, na otimização de investimentos e no fortalecimento da posição da Chilli Beans no mercado.


#### Persona 2 – Perfil Operacional de Loja (Diretamente Afetada)

![Ficha Ricardo](/assets/ficha_ricardo.png)

###### Perfil:
&nbsp;&nbsp;Ricardo, 38 anos, é Gerente Regional de Operações na rede Chilli Beans, responsável por diversas lojas (incluindo franquias e quiosques) em sua região. Ele começou sua carreira como gerente de uma loja individual e hoje supervisiona equipes de vendedores e gerentes locais, garantindo a execução das estratégias da marca no dia a dia.
Orientado a desempenho, Ricardo atua como ponte entre o corporativo e as lojas, aplicando as campanhas definidas pela matriz e ajustando-as à realidade local. Seu sucesso é medido por metas de vendas, taxa de conversão e crescimento de fluxo de clientes nas lojas sob sua gestão.

###### Dores e Necessidades:
&nbsp;&nbsp;Fluxo de Clientes e Vendas Inconsistentes: Ricardo lida com oscilações de movimento nas lojas – alguns pontos de venda enfrentam baixo fluxo e conversão abaixo da média. Essa imprevisibilidade o preocupa, pois impacta diretamente as metas mensais. Ele precisa de ferramentas preditivas que indiquem quais fatores locais (concorrência próxima, perfil demográfico da região, etc.) estão afetando o desempenho e como aumentar o tráfego qualificado em cada loja.

###### Execução de Estratégias Locais:
&nbsp;&nbsp;Muitas vezes, Ricardo recebe diretrizes genéricas do corporativo, mas sente falta de insights acionáveis para cada unidade. Por exemplo, quais lojas ou regiões responderiam melhor a uma promoção específica ou qual mix de produtos destacar em determinada localidade. Ele necessita de orientações customizadas por loja/território para executar ações de marketing local mais eficazes.

###### Pressão por Resultados Operacionais:
&nbsp;&nbsp;Como responsável direto pelos números de vendas, ele sofre pressão constante para atingir metas. Quando uma loja vai mal, Ricardo precisa identificar rapidamente a causa (e.g., equipe despreparada, estoque inadequado, localização pouco conhecida) e corrigir o rumo.
Isso cria a necessidade de monitoramento em tempo real e alertas do modelo sobre unidades com performance fora do padrão, permitindo ações corretivas proativas (treinamentos, redistribuição de investimento em mídia local, etc.).

###### Falta de Visibilidade do Cliente:
&nbsp;&nbsp;No contato com as lojas, Ricardo percebe que os times têm pouco conhecimento sobre o perfil dos clientes que as frequentam. Ele precisa de dados de comportamento de compra segmentados (preferências por categoria, frequência de visitas, geração) para apoiar os gerentes de loja em atender melhor o público – seja ajustando o atendimento, estoque ou ofertas de serviços (como exames ópticos nas óticas de rua).

###### Comportamentos:
&nbsp;&nbsp;Diariamente, Ricardo verifica relatórios de vendas de cada loja e compara com as metas, entrando em contato com gerentes locais para discutir desempenho. Ele costuma visitar lojas semanalmente, observando o movimento de clientes, a disposição de produtos e engajamento da equipe.
Quando identifica uma queda nas vendas, tende a agir de imediato: por exemplo, pode sugerir uma promoção relâmpago ou redistribuir produtos entre lojas para atender preferências regionais. Ricardo valoriza a experiência prática e o feedback de sua equipe, mas aceita cada vez mais o apoio de dados – recentemente passou a usar um dashboard interno fornecido pelo BI para visualizar KPIs regionais.
Em reuniões com o corporativo, ele compartilha desafios de campo e busca trazer sugestões do time de vendas, atuando como advogado das necessidades operacionais junto à estratégia central.

###### Impacto do Modelo:
&nbsp;&nbsp;Com o novo modelo preditivo, Ricardo passa a operar de forma muito mais informada e proativa. O sistema destaca para ele quais lojas na região têm conversão abaixo do esperado e sugere possíveis causas correlacionadas (por exemplo, preço médio baixo ou categoria X com pouca saída).
De posse dessa informação, Ricardo consegue agir preventivamente, direcionando treinamentos específicos ou campanhas locais antes que o problema se agrave. Além disso, o modelo identifica clusters territoriais responsivos a certas promoções – assim, Ricardo pode personalizar ações por loja, como implementar uma campanha jovem nas lojas próximas a universidades, ou reforçar estoque de óculos de grau em regiões com público de meia-idade.

&nbsp;&nbsp;Ao invés de “tiro no escuro”, suas decisões operacionais agora são guiadas por evidências: ele aloca melhor os recursos regionais, define metas realistas por praça e comunica ao time prioridades com embasamento (por exemplo, focar em melhorar conversão em vez de apenas atrair volume, se os dados indicarem essa necessidade).

&nbsp;&nbsp;Em suma, o modelo torna Ricardo mais ágil e eficiente na gestão: problemas são sinalizados com antecedência e oportunidades locais são mais facilmente identificadas, melhorando o desempenho geral das lojas.
Conclusão: Como gestor operacional diretamente afetado pelo projeto, Ricardo sente um alívio significativo em suas atividades. Suas dores – falta de previsibilidade e orientação local – são mitigadas por um fluxo constante de insights do modelo. Agora ele consegue equilibrar a experiência de campo com inteligência de dados, tornando-se capaz de antecipar tendências regionais e agir estrategicamente.

&nbsp;&nbsp;O resultado é um aumento na performance das lojas, uma execução mais alinhada às diretrizes corporativas e uma equipe de vendas mais confiante, pois Ricardo os apoia com informações claras sobre onde focar. Em última instância, o modelo preditivo eleva a qualidade do trabalho de Ricardo, que passa de um modo predominantemente reativo para um gerenciamento proativo e orientado a evidências, contribuindo para os objetivos maiores da Chilli Beans.

#### Persona 3 – Cliente Típico da Chilli Beans (Indiretamente Afetada)

![Marcos](/assets/ficha_marcos.png)

###### Perfil:
&nbsp;&nbsp;Marcos, 28 anos, é um cliente fiel e parte do público-alvo da Chilli Beans. Morador de uma grande cidade, ele trabalha na área de criação publicitária e valoriza estilo e inovação. Marcos já possui vários óculos de sol da marca, atraído pelo design ousado e pelas constantes novidades nas coleções.

&nbsp;&nbsp;Recentemente, começou a precisar usar óculos de grau para trabalho e lazer, mas ainda encara a Chilli Beans mais como uma marca de moda do que uma ótica confiável. Ele está sempre conectado – acompanha as redes sociais da empresa e recebe newsletters – e busca experiências de compra práticas e personalizadas, seja na loja física ou online.

###### Dores e Necessidades:
&nbsp;&nbsp;Relevância das Ofertas: Como consumidor jovem e antenado, Marcos fica frustrado com comunicações genéricas ou produtos que não combinam com seu estilo. Ele deseja receber ofertas e conteúdos alinhados aos seus gostos – por exemplo, armações de grau com design moderno ou promoções em óculos solares similares aos que já comprou – em vez de propaganda massiva. Sua dor é sentir-se “mais um” na base de clientes; sua necessidade é um tratamento personalizado, que reflita que a marca o conhece.

###### Dúvidas sobre Óculos de Grau:
&nbsp;&nbsp;Apesar de admirar a marca, Marcos hesita em comprar óculos de grau na Chilli Beans por incerteza quanto à qualidade das lentes e serviços ópticos. Ele precisa de confiança e informação: avaliações de outros clientes, garantia de qualidade e talvez serviços como teste de visão ou experimentação facilitada.
A dor aqui é a falta de credibilidade percebida da Chilli Beans no segmento de grau; a necessidade é ser convencido de que a marca pode atendê-lo tão bem em grau quanto em óculos de sol.

###### Experiência Omnicanal Fluida:
&nbsp;&nbsp;Marcos transita entre canais – pesquisa online e compra na loja, ou vice-versa. Ele se incomoda quando a experiência não é integrada (por exemplo, ver um modelo no Instagram que não encontra na loja física ou perder um desconto recebido por e-mail por expirar rapidamente).
Ele necessita de consistência e conveniência: que a jornada online-offline seja contínua, com estoque unificado e reconhecimento de seu perfil em qualquer canal. Sua expectativa é que a Chilli Beans facilite essa jornada, tornando-a simples e sem atritos.

###### Novidades e Tendências:
&nbsp;&nbsp;Como entusiasta de moda, Marcos valoriza estar por dentro das tendências. Uma de suas dores é perder lançamentos relevantes – às vezes fica sabendo tarde de alguma coleção especial ou colaboração da marca. Ele precisa que a Chilli Beans seja proativa em engajá-lo quando houver algo que se encaixe no seu interesse, seja via notificações no app, mensagens segmentadas ou eventos VIP para clientes.

###### Comportamentos:
&nbsp;&nbsp;Marcos verifica diariamente suas redes sociais e costuma interagir com marcas que admira. No Instagram, curte e comenta posts da Chilli Beans quando veem produtos de seu interesse. Visita lojas físicas em shoppings aos fins de semana para experimentar novidades, mas também compra pelo site se encontrar conveniência ou promoções exclusivas online.
Ele valoriza programas de fidelidade e costuma comparar benefícios; por exemplo, já usou cupons de desconto enviados por e-mail. Seu comportamento de compra é impulsionado por estilo e necessidade prática: troca de óculos solares anualmente por tendência, e agora pesquisa opções de óculos de grau que unam design e conforto.

&nbsp;&nbsp;Marcos compartilha feedback nas lojas ou redes – se tem boa experiência, recomenda aos amigos, mas também expressa rapidamente insatisfação se algo não corresponde ao esperado. Em suma, é um cliente engajado, com voz ativa e opções de escolha, que a marca precisa cativar a cada interação.

###### Impacto do Modelo:
&nbsp;&nbsp;Indiretamente, Marcos passa a vivenciar uma Chilli Beans mais sintonizada com ele graças às entregas do projeto preditivo. As análises de público ideal permitem que a marca personalize a comunicação e as ofertas para perfis como o de Marcos. Isso significa que ele começa a receber promoções de produtos que genuinamente lhe interessam – por exemplo, ofertas em armações de grau com design arrojado adequadas à sua faixa etária, ou convites para eventos de lançamento que combinam com seu estilo. Ele percebe que as campanhas ficam mais relevantes: menos spam de itens aleatórios e mais conteúdo sobre coleções ou serviços que fazem sentido para ele.

&nbsp;&nbsp;Além disso, com o aumento do fluxo nas lojas de rua e melhorias no sortimento impulsionadas pelo modelo, Marcos encontra lojas bem preparadas – vendedores que entendem suas preferências (orientados pelos insights do modelo sobre comportamento de compra por geração) e produtos disponíveis conforme a demanda local.

&nbsp;&nbsp;Se antes ele tinha dúvidas sobre comprar óculos de grau na Chilli Beans, agora ele nota ações específicas da marca para sanar isso (como comunicações destacando garantia das lentes ou parcerias com especialistas), fruto das estratégias geradas pelo projeto para crescer a categoria de grau.
Em essência, o modelo preditivo melhora a experiência de Marcos como cliente: a marca se antecipa às suas necessidades, seja apresentando a ele o produto certo no momento certo, seja oferecendo um atendimento mais afinado com seu perfil.

###### Conclusão:
&nbsp;&nbsp;Como cliente típico indiretamente afetado pelo projeto, Marcos sente a Chilli Beans mais próxima e relevante no seu dia a dia. Ele não conhece os bastidores do modelo preditivo, mas se beneficia de seus resultados – recebe interações mais personalizadas, vê a marca evoluindo também no segmento de grau (o que aumenta sua confiança em finalmente adquirir óculos de grau da Chilli Beans) e desfruta de uma jornada de compra mais integrada e agradável.

&nbsp;&nbsp;Com isso, Marcos tende a aumentar sua fidelidade à marca, engajando-se com as novidades e permanecendo por mais tempo como cliente. Em última análise, a satisfação de personas como Marcos confirma o sucesso das entregas do projeto preditivo: ao atender melhor o público-alvo, a Chilli Beans fortalece seu posicionamento e impulsiona seu crescimento de forma sustentável.


#### 4.1.7. Jornadas do Usuário

&nbsp;&nbsp;A jornada do usuário é uma representação visual de todo o caminho que uma pessoa percorre ao interagir com um produto, serviço ou empresa para atingir um objetivo específico. Ela mapeia cada passo e ponto de contato, desde o momento da descoberta até o pós-uso, detalhando não apenas as ações realizadas, mas também os pensamentos, emoções e motivações do usuário em cada etapa. O objetivo de mapear essa jornada é obter uma compreensão profunda da experiência do cliente, identificar pontos de atrito, necessidades não atendidas e oportunidades de melhoria, permitindo que as empresas otimizem seus processos e criem soluções mais eficazes e satisfatórias. Dessa forma, segue abaixo as jornadas das personas mapeadas para o projeto.

#### Jornada 1 (Ricardo) — Aumentar fluxo e conversão nas Óticas de Rua

<div align="center">

<sub>Figura x: Jornada do Usuário (Ricardo) </sub>

<img src="../../assets/jornada-ricardo.png" style=width:100%>

<sub>Fonte: Autoria própria (2025)</sub>

</div>

##### Persona & Contexto

 &nbsp;&nbsp;Gerente de Loja (Ótica de Rua). Opera uma unidade de rua ainda pouco familiar ao público local, com oscilação de tráfego e conversão aquém do potencial; precisa atrair fluxo qualificado e converter.

##### História de Usuário
&nbsp;&nbsp;Como gerente de uma Ótica Chilli Beans de rua, quer atrair mais clientes qualificados e elevar a conversão, para bater metas e sustentar o crescimento da unidade.

##### Problema / Dor
&nbsp;&nbsp;Lojas de rua requerem decisões baseadas em fatores do entorno (perfil socioeconômico, concorrência, mobilidade/foot traffic). A ausência de histórico robusto dificulta priorizar ações e investimentos; sem um diagnóstico preditivo, decisões ficam no “achismo”.

##### Objetivo da Jornada
&nbsp;&nbsp;Elevar tráfego e conversão em lojas de rua, orientando onde agir (regiões e unidades prioritárias) e como agir (ações e campanhas por cluster territorial).

##### Benefícios Esperados
&nbsp;&nbsp;Mais visitas, maior conversão, priorização de investimento de mídia local, insumos para expansão (site selection) e comunicação regional.

##### Dados e Recursos
&nbsp;&nbsp;Histórico de vendas por loja e categoria; dados demográficos; tipologia do PDV (rua, shopping, quiosque); indicadores de preço e mix; sinais externos (concorrência, pontos de interesse).

##### Abordagem (Escopo Macro)
&nbsp;&nbsp;Modelagem preditiva e análises geoespaciais para: identificar fatores críticos de sucesso por região; clusterizar lojas com contextos similares; detectar outliers; recomendar ações locais (campanhas, eventos, vitrine/mix); e gerar mapas de oportunidade.


##### Etapas / Questões-chave
1) Hotspots de fluxo qualificado.
2) Fatores locais que apresentam desempenho.
3) Lojas abaixo da meta e causas prováveis.
4) Clusters mais responsivos a cada tipo de promoção.
5) Onde investir em awareness e tráfego.
6) Relação entre categorias e demografia do entorno.

##### Resultados e Entregáveis
&nbsp;&nbsp;Ranking de oportunidades por região/loja; meta de conversão “ideal” por unidade; alertas de risco/anomalias; recomendações de ação por cluster; painéis e mapas comparativos.

##### Restrições / Exclusões
 &nbsp;&nbsp;Dados minimamente estruturados; sem integração em produção/CRM; sem modelos complexos de deep learning; sem análise de margem por SKU; sem uso de dados sensíveis; sem envio de dados a IAs de terceiros.

##### Stakeholders
 &nbsp;&nbsp;Dados/BI (modelagem, insights), Marketing/Expansão (mídia e site selection), Operações (execução nas lojas), Liderança (patrocínio e priorização).

#### Jornada 2 (Juliana) — Definir público ideal e orientar decisões estratégicas de expansão e marketing

<div align="center">

<sub>Figura x: Jornada do Usuário (Juliana) </sub>

<img src="../../assets/jornada-juliana.png" style=width:100%>

<sub>Fonte: Autoria própria (2025)</sub>

</div>

##### Persona & Contexto
&nbsp;&nbsp;Juliana, Diretora de Estratégia e Expansão, precisa de informações acionáveis em nível agregado para priorizar regiões, justificar investimentos e alinhar marketing, expansão e finanças.

##### História de Usuário
&nbsp;&nbsp;Como Diretora de Estratégia, quer acessar perfis de cliente e cenários de potencial por região para priorizar aberturas, campanhas e alocação de orçamento com previsão de ROI.

##### Problema / Dor
&nbsp;&nbsp;Decisões estratégicas exigem trade-offs entre custo de abertura, risco e retorno; sem estimativas claras e cenários, investimentos podem falhar ou gerar baixa rentabilidade.

##### Objetivo da Jornada
&nbsp;&nbsp;Gerar segmentações estratégicas e simulações de cenário que permitam a Juliana escolher praças, formatos de loja e campanhas com previsão de impacto financeiro e operacional.

##### Benefícios Esperados
- Priorização objetiva de pontos de expansão com estimativa de receita.
- Alocação de mídia baseada em clusters com maior propensão de conversão.
- Relatórios executivos claros para tomada de decisão e aprovação de orçamento.

##### Dados e Recursos
&nbsp;&nbsp;Vendas agregadas por região/loja, dados demográficos regionais, desempenho por formato de PDV, histórico de campanhas, custos operacionais e métricas financeiras (ticket médio, margem, LTV).

##### Abordagem (Escopo Macro)
&nbsp;&nbsp;Segmentação estratégica + modelagem de potencial de mercado + simulações "what‑if" (cenários otimista/realista/pessimista) + dashboards executivos com detalhamento por praça.

##### Etapas / Questões-chave
1) Identificar clusters regionais com maior potencial de receita e margem
2) Projetar incremento de novos pontos de venda em diferentes cenários
3) Avaliar canais e campanhas com melhor ROI por cluster
4) Medir risco de concorrência entre franquias próximas
5) Definir KPI´s para suportar decisões
6) Estruturar plano de piloto com critérios de escalonamento

##### Resultados e Entregáveis
&nbsp;&nbsp;Ranking de praças com previsão de receita e payback; personas estratégicas; dashboards de cenários; roteiro de piloto com métricas e checklist de pré‑abertura.

##### Restrições / Exclusões
&nbsp;&nbsp;Sem integração em tempo real com ERP/CRM nesta fase; análises dependem da qualidade dos dados históricos disponíveis; não inclui decisões de pricing por SKU detalhado.

##### Stakeholders
&nbsp;&nbsp;Expansão, Marketing/CRM, Finanças, Dados/BI.

#### Jornada 3 (Marcos) — Experiência omnicanal e conversão para cliente jovem (Marcos)

<div align="center">

<sub>Figura x: Jornada do Usuário (Marcos) </sub>

<img src="../../assets/jornada-marcos.png" style=width:100%>

<sub>Fonte: Autoria própria (2025)</sub>

</div>

##### Persona & Contexto
&nbsp;&nbsp;Marcos, cliente jovem e conectado, busca estilo e conveniência; tem dúvidas sobre comprar óculos de grau na marca e valoriza experiência digital integrada e confiança na qualidade.

##### História de Usuário
&nbsp;&nbsp;Como cliente, quer encontrar recomendações de armações que combinem com seu estilo, experimentar virtualmente e ter confiança sobre a qualidade das lentes e serviços para finalizar a compra com facilidade.

##### Problema / Dor
&nbsp;&nbsp;Falta de confiança em lentes de grau da marca, obstáculos da jornada online para a loja (disponibilidade, prova do produto, exames), e comunicações genéricas que não refletem seu estilo.

##### Objetivo da Jornada
&nbsp;&nbsp;Reduzir dificuldades na descoberta e compra de grau, aumentar taxa de conversão de interessados em grau e melhorar percepção de confiança da marca.

##### Benefícios Esperados
- Aumento da conversão e do tíquete médio.
- Melhora na experiência multicanal.
- Melhoria do NPS entre clientes jovens.

##### Dados e Recursos
&nbsp;&nbsp;Histórico de navegação e compras, preferências (estilo, cor, tamanho), disponibilidade de estoque por loja, avaliações e provas virtuais, dados de campanhas segmentadas.

##### Abordagem (Escopo Macro)
&nbsp;&nbsp;Personalização de conteúdo e ofertas + ferramentas de experimentação de provas virtuais + jornadas de comunicação segmentadas + facilitação de serviços (agendamento de exame, retirada/reserva em loja).

##### Etapas / Questões-chave
1) Personalizar recomendações de armações com base em histórico e comportamento digital
2) Oferecer prova virtual e agendamento simplificado para reduzir incerteza
3) Testar incentivos promocionais
4) Medir impacto omnicanal
5) Produzir conteúdos educativos sobre qualidade de lentes, garantia e avaliações
6) Integrar disponibilidade de estoque entre canais em tempo real

##### Resultados e Entregáveis
&nbsp;&nbsp;Motor de recomendações personalizadas; experiência AR implementada nos canais digitais; fluxo de agendamento/checkout multicanal; campanha segmentada para conversão em grau; KPIs: taxa de conversão, tempo médio até compra, NPS pós‑compra, taxa de comparecimento a exames.

##### Restrições / Exclusões
&nbsp;&nbsp;Não envolve tratamento de dados sensíveis de saúde sem consentimento explícito; requer consentimento para personalização e uso de histórico; integrações técnicas dependem de disponibilidade de APIs de estoque/loja.

##### Stakeholders
&nbsp;&nbsp;Marketing/CRM, Produto (UX), Operações de loja, Equipe de Experiência do Cliente, Dados/BI.

###### Conclusão

&nbsp;&nbsp;A conclusão do mapeamento da jornada para as diferentes personas marca a transição de um entendimento abstrato para uma visão concreta e empática da experiência do usuário. Ao visualizar os caminhos, pontos de contato e estados emocionais de cada perfil, obtivemos clareza sobre os desafios e oportunidades que nossa solução enfrenta. A análise consolidada das jornadas revelou padrões universais e necessidades específicas, fornecendo um panorama completo que nos permite tomar decisões mais informadas. Esta análise será utilizada como um pilar estratégico, destinada a orientar o processo de design, a priorização de novas funcionalidades e a garantir que cada etapa do desenvolvimento futuro esteja alinhada com a criação de valor real para nossos usuários.

#### 4.1.8 Política de Privacidade e Proteção de Dados Pessoais

&nbsp;&nbsp;Nesta seção, é apresentada a Política de Privacidade e Proteção de Dados Pessoais que rege o tratamento de informações no âmbito do projeto de análise preditiva da Chillibinos. Tendo como base a Lei Geral de Proteção de Dados Pessoais (LGPD), este documento estabelece as diretrizes para garantir que toda coleta, uso, armazenamento e compartilhamento de dados ocorra de maneira ética, transparente e segura. Os subtópicos a seguir detalham os princípios, os direitos dos titulares e as medidas técnicas e administrativas implementadas para assegurar a conformidade e proteger as informações contra acessos não autorizados e outros riscos.

##### 4.1.8.1 Introdução e Objetivos

&nbsp;&nbsp;A Chillibinos, comprometida com a privacidade e a proteção dos dados pessoais tratados em seus projetos de análise preditiva, estabelece esta Política de Privacidade em conformidade com a Lei Geral de Proteção de Dados Pessoais (LGPD – Lei nº 13.709/2018). O objetivo é garantir transparência, segurança e respeito aos direitos dos titulares de dados, detalhando como ocorre a coleta, uso, armazenamento e compartilhamento de informações pessoais no contexto deste projeto.

##### 4.1.8.2 Definições e Conceitos

- **Dado pessoal:** Informação relacionada a pessoa natural identificada ou identificável.
- **Dado sensível:** Dado pessoal sobre origem racial ou étnica, convicção religiosa, opinião política, saúde, vida sexual, dado genético ou biométrico.
- **Titular:** Pessoa natural a quem se referem os dados pessoais.
- **Controlador:** Pessoa natural ou jurídica responsável pelas decisões referentes ao tratamento de dados pessoais.
- **Operador:** Pessoa natural ou jurídica que realiza o tratamento de dados em nome do controlador.
- **Encarregado (DPO):** Pessoa indicada pelo controlador para atuar como canal de comunicação entre o controlador, os titulares e a ANPD.

##### 4.1.8.3 Princípios de Tratamento

&nbsp;&nbsp;O tratamento de dados pessoais neste projeto segue os princípios da LGPD:

- **Finalidade:** Os dados são coletados e utilizados apenas para fins específicos e legítimos, como análise preditiva de vendas, comportamento do consumidor e otimização de processos internos.
- **Adequação:** O tratamento é compatível com as finalidades informadas ao titular.
- **Necessidade:** Apenas os dados estritamente necessários são coletados e tratados.
- **Livre acesso:** O titular pode consultar, de forma facilitada e gratuita, quais dados seus são tratados.
- **Qualidade dos dados:** São adotadas medidas para garantir exatidão, clareza e atualização dos dados.
- **Transparência:** Informações claras sobre o tratamento são disponibilizadas aos titulares.
- **Segurança:** Medidas técnicas e administrativas são aplicadas para proteger os dados.
- **Prevenção:** Adoção de ações para prevenir danos em virtude do tratamento de dados.
- **Não discriminação:** Os dados não são utilizados para fins discriminatórios.
- **Responsabilização e prestação de contas:** A Chilli Beans adota práticas que comprovam a observância e o cumprimento da LGPD.

##### ******* Direitos dos Titulares

&nbsp;&nbsp;Os titulares de dados pessoais têm direito a:

- Confirmação da existência de tratamento.
- Acesso aos dados.
- Correção de dados incompletos, inexatos ou desatualizados.
- Anonimização, bloqueio ou eliminação de dados desnecessários, excessivos ou tratados em desconformidade.
- Portabilidade dos dados a outro fornecedor, mediante requisição expressa.
- Eliminação dos dados pessoais tratados com consentimento, exceto nas hipóteses legais.
- Informação sobre compartilhamento de dados.
- Revogação do consentimento, quando aplicável.

##### ******* Medidas de Segurança

&nbsp;&nbsp;A Chillibinos adota medidas técnicas e organizacionais para proteger os dados pessoais, incluindo:

- Controle de acesso restrito a dados pessoais.
- Criptografia de dados sensíveis em trânsito e em repouso.
- Monitoramento e auditoria de acessos.
- Treinamento periódico das equipes.
- Políticas de backup e recuperação de dados.
- Adoção de padrões e certificações de segurança da informação, quando aplicável.

##### ******* Responsabilidades e Contato

- **Controlador:** Chillibinos
- **Operadores:** Empresas e profissionais contratados para desenvolvimento e manutenção do projeto preditivo, sob supervisão da Chilli Beans.
- **Encarregado (DPO):** Responsável por receber demandas dos titulares e da ANPD, bem como orientar a equipe sobre boas práticas de proteção de dados.

##### ******* Atualizações da Política

&nbsp;&nbsp;Esta política poderá ser revisada periodicamente para garantir sua aderência à legislação vigente e às melhores práticas de mercado.

##### Conclusão

&nbsp;&nbsp;Portanto, a Política de Privacidade e Proteção de Dados Pessoais estabelecida constitui um pilar fundamental para a governança e a execução do projeto de análise preditiva. Ao formalizar os princípios, garantir os direitos dos titulares e implementar rigorosas medidas de segurança, a Chillibinos não apenas assegura a conformidade com a LGPD, mas também reforça seu compromisso com a ética e a confiança. A adesão a estas diretrizes é mandatória para todos os envolvidos e essencial para a integridade e o sucesso das operações.

**Data da última atualização:** Agosto de 2025

### 4.2. Compreensão dos Dados

Nesta seção, apresentamos a compreensão dos dados utilizada no pipeline do projeto, com foco no conjunto de vendas da Chilli Beans. O dataset consolidado resultou em 1.330 cidades e 928 lojas analisadas ao longo de 5 dias, com receitas tratadas em centavos. As análises exploratórias foram conduzidas em Python (PANDAS; HAN; KAMBER; PEI, 2011) com apoio de gráficos e testes estatísticos não paramétricos (JORDAN; MITCHELL, 2015), além de técnicas de pré-processamento e codificação (scikit-learn; WITTEN et al., 2016).

#### 4.2.1. Exploração de dados

O fluxo metodológico adotado seguiu as seguintes etapas:

1º Ingestão da base em Excel localizada em data/raw
2º Padronização de colunas
3º Limpeza e tipagem
4º Análise exploratória (EDA) e métricas descritivas
5º Construção de baselines e modelos
6º Interpretação via SHAP
7º Análises geográficas e mapas
8º Exportação de resultados em reports/YYYY-MM-DD

#### 4.2.2. Pré‑processamento dos dados

O pré-processamento foi conduzido em múltiplas etapas. Inicialmente, datas foram padronizadas com dayfirst=True, garantindo consistência temporal e eliminando registros inválidos. Na sequência, os valores monetários passaram por limpeza textual (remoção de símbolos “R$”, pontos e vírgulas) e conversão numérica robusta (pd.to_numeric(errors="coerce")), seguidos da transformação para centavos.

Thresholds operacionais (inserir): remover colunas com >50% missing; remover linhas somente se `data` ou `id_loja` ausentes; imputação operacional definida como `qtd`=0 e `valor`=mediana por grupo ou mediana global conforme análise de missingness; outliers detectados por MAD com score > 3.

Tratamento de outliers (inserir): aplicar winsorização em `valor` nos percentis 1%/99% e preservar a coluna original `valor_raw` para auditoria; comparativos pré/post salvos em `reports/2025-08-15/plots/`.

Artefatos e versão (inserir): salvar pipeline de pré-processamento em `artifacts/preprocessing_pipeline.joblib` e `data/clean/cleaned.parquet` com timestamp e hash; registrar versões em `requirements.txt` e seed usada.

No tratamento de nulos, registros sem data ou loja foram descartados por inviabilizarem análises temporais e geográficas. Para variáveis auxiliares, como qtd, optou-se por preencher valores ausentes com zero, assumindo ausência de venda. Duplicatas exatas foram removidas e a base foi ordenada temporalmente.

Outliers foram identificados por meio do *z-score robusto (MAD)*, e valores extremos foram ajustados via winsorização nos percentis 1 e 99. Essa escolha preserva os registros e reduz distorções. As variáveis categóricas foram codificadas com técnicas apropriadas: uf e Tipo_PDV receberam codificação one-hot, enquanto cidade foi tratada por agrupamento regional, evitando explosão de dimensões dada sua alta cardinalidade. Por fim, variáveis numéricas foram normalizadas (StandardScaler) para adequação a algoritmos sensíveis à escala.

Essas etapas foram conduzidas com foco na robustez e coerência metodológica, assegurando dados preparados para modelagem preditiva.

#### 4.2.3. Hipóteses

Com base na análise exploratória e nos objetivos do projeto, definimos três hipóteses testáveis:

- *H1:* O estado de São Paulo apresenta receita média por loja significativamente maior do que outras UFs.
Justificativa: SP concentra o maior mercado consumidor do país e abriga lojas emblemáticas da marca.
Resultado: Confirmada (p ≈ 2e-40). As lojas de SP superaram de forma significativa as de outros estados.

- *H2:* O padrão de vendas nos finais de semana difere significativamente dos dias úteis.
Justificativa: Supunha-se maior fluxo aos sábados e domingos, em função do lazer e consumo em shoppings.
Resultado: Rejeitada (p ≈ 0,8). Não houve diferença estatisticamente significativa entre dias úteis e finais de semana.

- *H3:* Cidades com múltiplas lojas concentram maior receita total e também maior receita média por loja do que cidades com loja única.
Justificativa: Centros urbanos com mais de uma unidade tendem a concentrar maior demanda.
Resultado: Confirmada (p ≈ 0). Cidades com múltiplas lojas apresentaram superioridade tanto em receita agregada quanto em receita média por loja.

Os resultados obtidos reforçam a importância de São Paulo e dos grandes centros urbanos como polos estratégicos da marca. A ausência de diferença significativa em H2 sugere que o consumo da Chilli Beans é relativamente constante ao longo da semana. Já H3 confirma a viabilidade da expansão em cidades já atendidas. Como recomendação, propõe-se incorporar futuramente uma hipótese sobre o desempenho dos diferentes formatos de PDV (rua, shopping, quiosque, Eco), em alinhamento direto com o objetivo do projeto de aumentar o fluxo e a conversão nas óticas de rua.

### 4.3. Preparação dos Dados e Modelagem

&nbsp;&nbsp;Esta seção apresenta a abordagem híbrida adotada no projeto InfoPepper, combinando técnicas supervisionadas e não supervisionadas para análise territorial. A metodologia integra modelos preditivos robustos com análises exploratórias avançadas, proporcionando insights acionáveis para decisões de expansão territorial da Chilli Beans.

#### 4.3.1. Abordagem Metodológica Híbrida

&nbsp;&nbsp;O projeto adota uma estratégia metodológica híbrida que combina:

**Modelagem Supervisionada:**
- **Análise Territorial:** Modelo de regressão Random Forest para predição de potencial de receita por região

**Modelagem Não Supervisionada:**
- **Segmentação Territorial:** Clustering K-Means para identificação de padrões regionais complementares

&nbsp;&nbsp;Esta abordagem permite capturar tanto padrões supervisionados baseados em targets específicos quanto estruturas latentes nos dados que podem revelar insights não óbvios para o negócio. O projeto focou principalmente na análise territorial, implementando tanto abordagem supervisionada quanto não supervisionada para comparação e validação cruzada dos resultados.

#### 4.3.2. Organização dos Dados

**A) Modelo Supervisionado - Análise Territorial**

&nbsp;&nbsp;Para a análise territorial, foi utilizada validação cruzada 5-fold diretamente sobre o dataset regional agregado:

- **Dataset Regional:** 9 regiões (grupos sintéticos) analisadas
- **Features:** Agregações de receita, volume transacional e características regionais
- **Validação:** Cross-validation 5-fold com KFold(shuffle=True, random_state=42)
- **Normalização:** StandardScaler aplicado às features antes da modelagem

&nbsp;&nbsp;A abordagem regional agregou dados transacionais por grupos geográficos, criando um dataset compacto mas representativo para análise de potencial territorial.

**B) Modelo Não Supervisionado - Segmentação Territorial**

&nbsp;&nbsp;Para o clustering territorial, o mesmo dataset regional foi utilizado:

- **Dataset Regional:** 9 regiões com features agregadas
- **Normalização:** StandardScaler para equalizar escalas entre diferentes métricas
- **Algoritmo:** K-Means com diferentes valores de K testados (3 a 8)
- **Seleção de K:** Baseada no Silhouette Score máximo

#### 4.3.3. Modelagem para o Problema e Seleção de Features

**A) Análise Territorial (Supervisionado)**

&nbsp;&nbsp;**Linha de Raciocínio:** O modelo territorial visa predizer o potencial de receita de uma região baseado em agregações históricas de performance. A abordagem utiliza dados internos da Chilli Beans agregados por região para gerar um score de potencial territorial.

**Features Implementadas:**
- **Agregações de Receita:** `valor_sum`, `valor_mean`, `valor_median`, `valor_count` - Indicadores de performance histórica
- **Estatísticas Descritivas:** `valor_std` - Variabilidade da receita regional
- **Target:** Receita total agregada por região (valor_sum)

**Justificativa:** As features foram derivadas diretamente dos dados transacionais disponíveis, focando em métricas de receita que são diretamente relevantes para decisões de expansão territorial. A simplicidade do conjunto de features garante interpretabilidade e robustez do modelo.

**B) Segmentação Territorial (Não Supervisionado)**

&nbsp;&nbsp;**Linha de Raciocínio:** A segmentação territorial complementa a análise supervisionada, identificando grupos homogêneos de regiões com características similares de performance, permitindo estratégias diferenciadas por cluster territorial.

**Features Utilizadas:**
- **Mesmas features do modelo supervisionado:** Agregações de receita (sum, mean, median, std, count)
- **Normalização:** StandardScaler aplicado para equalizar escalas
- **Objetivo:** Identificar padrões regionais não capturados pela análise supervisionada

**Justificativa:** Utilizar as mesmas features permite comparação direta entre abordagens supervisionada e não supervisionada, validando insights através de concordância entre métodos independentes.

#### 4.3.4. Métricas dos Modelos

**A) Modelos Supervisionados - Métricas de Regressão (Análise Territorial)**

&nbsp;&nbsp;Para o modelo de análise territorial Random Forest, foram obtidas as seguintes métricas via validação cruzada 5-fold:

1. **RMSE (Root Mean Square Error):** 1669.34 ± 626.16
   - Mede o erro quadrático médio na escala original (centavos)
   - Valor indica erro médio de aproximadamente R$ 16,69 por região

2. **MAE (Mean Absolute Error):** 1505.88 ± 594.24
   - Mede o erro absoluto médio, mais robusto a outliers
   - Erro médio absoluto de aproximadamente R$ 15,06 por região

3. **R² (Coeficiente de Determinação):** NaN ± NaN
   - Métrica não calculável devido ao tamanho reduzido do dataset regional (9 regiões)
   - Limitação esperada em datasets pequenos com validação cruzada

**B) Modelos Não Supervisionados - Métricas de Clustering Territorial**

&nbsp;&nbsp;Para a segmentação territorial K-Means, foram testados diferentes valores de K com os seguintes resultados:

**Análise de K:**
- K=3: Inertia=38.43, Silhouette=0.248
- K=4: Inertia=26.45, Silhouette=0.264
- **K=5: Inertia=10.74, Silhouette=0.331** ← **SELECIONADO**
- K=6: Inertia=4.90, Silhouette=0.263
- K=7: Inertia=1.95, Silhouette=0.240
- K=8: Inertia=0.48, Silhouette=0.121

**Métricas do Modelo Final (K=5):**
1. **Silhouette Score:** 0.331
   - Valor moderado indicando separação razoável entre clusters territoriais
   - Melhor valor obtido entre as opções testadas

2. **Inertia:** 10.74
   - Compacidade adequada dos clusters territoriais
   - Redução significativa comparada a K menores

#### 4.3.6. Primeiro Modelo Candidato e Discussão dos Resultados

**Análise Territorial - Random Forest Regressor**

&nbsp;&nbsp;**Modelo Selecionado:** Random Forest com 400 estimadores, profundidade máxima de 10, min_samples_split=5, random_state=42.

**Discussão dos Resultados:**
- **Erro Moderado:** RMSE de 1669.34 centavos (≈R$ 16,69) representa erro aceitável considerando a escala de receita regional
- **Consistência:** MAE de 1505.88 centavos próximo ao RMSE indica distribuição de erros sem outliers extremos
- **Limitação de R²:** Métrica não calculável devido ao dataset pequeno (9 regiões), limitação técnica esperada
- **Estabilidade:** Desvios padrão moderados (±626 para RMSE) indicam variabilidade controlada entre folds

**Interpretação de Negócio:**
- O modelo consegue ranquear regiões por potencial, com Grupo_9 (score 10.0) e Grupo_8 (score 9.8) no topo
- Concordância de 88.9% entre métodos supervisionado e não supervisionado valida os resultados
- Ranking territorial fornece base sólida para decisões de expansão, priorizando regiões de maior potencial

**Limitações Identificadas:**
- Dataset regional pequeno (9 regiões) limita robustez estatística
- Ausência de dados externos (demografia, concorrência) reduz poder preditivo
- Necessidade de validação com dados de novas aberturas para confirmar eficácia

**Nota Importante sobre o Escopo do Projeto:**
&nbsp;&nbsp;Embora a documentação inicial mencione três modelos (análise territorial, segmentação de clientes e identificação de leads), a implementação atual focou prioritariamente na **análise territorial**. Os notebooks de segmentação de clientes e identificação de leads foram estruturados como templates/frameworks, mas não foram executados com dados reais devido a limitações de tempo e escopo do projeto. O modelo territorial implementado demonstra a viabilidade da abordagem híbrida e serve como prova de conceito para os demais modelos.

### 4.4 Comparação do Modelo

Nesta seção, comparamos candidatos de modelagem para o problema central de regressão do projeto (estimativa de potencial/receita a partir de variáveis territoriais e operacionais). Priorizamos métricas e procedimentos alinhados ao impacto de negócio e à robustez estatística.

4.4.1. Métricas e justificativas
- RMSE (Root Mean Squared Error): penaliza mais fortemente grandes desvios e, portanto, é mais sensível a erros que poderiam distorcer metas e orçamentos. Para a Chilli Beans, minimizar grandes erros é crucial em decisões de expansão e alocação de mídia regional.
- MAE (Mean Absolute Error): interpreta-se diretamente na escala do alvo e é mais robusto a outliers; usamos como métrica de apoio para comunicar erro típico de previsão.
- R²: proporção da variância explicada, útil como resumo global de ajuste do modelo para comparação entre algoritmos.
Observação técnica: a API do scikit‑learn expressa perdas como escores negativos (e.g., “neg_root_mean_squared_error”) durante a validação cruzada; nas tabelas de resultados reportamos o valor absoluto para facilitar a leitura executiva.

4.4.2. Modelos candidatos e tuning de hiperparâmetros
A seleção contemplou três famílias complementares de algoritmos para dados tabulares:
- Regressão Linear (LinReg): baseline paramétrico, interpretável, assume relação linear entre preditores e alvo. Tuning: sem hiperparâmetros críticos além de regularização implícita via pré‑processamento.
- Random Forest Regressor (RF): conjunto de árvores com bootstrap; modela não‑linearidades e interações; robusto a escalas e outliers. Tuning (Grid/Random): n_estimators ∈ {300, 600, 900}; max_depth ∈ {None, 10, 20, 40}; min_samples_leaf ∈ {1, 2, 4}.
- Support Vector Regressor (SVR, kernel RBF): margens máximas com kernel não‑linear. Tuning (Grid): C ∈ {1, 5, 10, 20}; gamma ∈ {scale, 0.1, 0.01}; epsilon ∈ {0.1, 0.2, 0.5}.
Procedimento: validação cruzada 5‑fold (shuffle, random_state=42). Grades pequenas foram avaliadas por Grid Search; grades maiores por Randomized Search (até 20 amostras). As execuções respeitam seeds para reprodutibilidade.

4.4.3. Resultados e comparação (validação cruzada)
Validação das métricas reportadas (baseline): conforme reports/2025‑08‑15/tables/algorithm_ranking.csv, as linhas a seguir confirmam os valores citados (lembrando que o RMSE é salvo como negativo pelo scikit‑learn e aqui é apresentado em valor absoluto):
- LinReg — linha 2 do CSV: rmse_mean = −0,035998 → RMSE ≈ 0,0360; r2_mean = 0,996228 → R² ≈ 0,9962
- RF — linha 3 do CSV: rmse_mean = −0,014350 → RMSE ≈ 0,0144; r2_mean = 0,999171 → R² ≈ 0,9992
- SVR (RBF) — linha 4 do CSV: rmse_mean = −0,149467 → RMSE ≈ 0,1495; r2_mean = 0,935731 → R² ≈ 0,9357
Visualizações associadas: reports/2025‑08‑15/plots/model_comparison/bar_rmse.png (baseline) e, após tuning no Colab, reports/2025‑08‑15/plots/model_comparison/bar_rmse_tuned.png.

Referências diretas ao código/notebooks:
- notebooks/model_comparison_colab.ipynb — célula 9 ("Validação Cruzada – baseline"): a variável res_df consolida as métricas por modelo e é persistida como algorithm_ranking_baseline.csv. Na célula 11 ("Tuning"), a variável best_df reúne as métricas pós‑tuning e salva algorithm_ranking_tuned.csv.
- notebooks/03_model_development_comparison.ipynb — célula de validação cruzada (variável res_df) gera o artefato reports/2025‑08‑15/tables/algorithm_ranking.csv (baseline utilizado acima).

*******. Análise de overfitting (detecção, estabilidade e mitigação)
- Treino vs. Validação: no estado atual, os notebooks registram apenas test_score por fold (return_train_score=False em cross_validate). Para realizar a checagem direta de sobreajuste, habilite return_train_score=True nas células de validação cruzada:
  • model_comparison_colab.ipynb — célula 8 (baseline) e célula 10 (tuning), instrução cvres = cross_validate(..., return_train_score=True, ...); depois compare mean(train_*) vs. mean(test_*).
- Estabilidade entre folds: o desvio‑padrão das métricas em CV é um indicador de robustez. No baseline (algorithm_ranking.csv):
  • LinReg — rmse_std ≈ 0,00367 (linha 2), baixa variabilidade relativa (estável);
  • RF — rmse_std ≈ 0,00902 (linha 3), estável no contexto dos valores (sinaliza boa generalização);
  • SVR — rmse_std ≈ 0,00505 (linha 4), estável, porém com erro médio mais alto.
- Interpretação de generalização: RF apresenta menor erro médio com variação moderada — indício positivo de bom viés‑variância. LinReg mantém erro baixo e variação muito baixa, adequado como baseline. SVR apresenta maior erro e estabilidade razoável, mas não supera RF/LinReg no recorte atual.
- Recomendações de mitigação (quando detectado overfitting):
  • Random Forest: reduzir profundidade (max_depth), aumentar min_samples_leaf, aumentar o n_estimators e considerar limitar max_features;
  • SVR: ajustar C (menor) e gamma (maior regularização), revisar escala e reduzir dimensionalidade (PCA/seleção de features);
  • Fluxo: adotar validação estratificada e/ou nested CV; reservar teste independente (já disponível em data/processed/test_set.csv); realizar análise de curvas de aprendizado (learning_curve) e auditorias de leakage;
  • Documentação: persistir train_score em CSV e anexar gráficos de distribuição fold‑a‑fold para auditoria contínua.

Interpretação executiva:
- O Random Forest apresenta o menor erro (RMSE) e maior R², sugerindo melhor capacidade de capturar não‑linearidades e interações entre drivers de negócio (ex.: preço/ desconto, tipologia de PDV, efeitos territoriais por UF/região). A estabilidade fold‑a‑fold indica bom potencial de generalização.
- A Regressão Linear performa como baseline competitivo (baixo RMSE, alta estabilidade), útil como referência e monitor de drift.
- O SVR com kernel RBF é sensível à escolha de hiperparâmetros e, no recorte atual, não superou RF; permanece como alternativa quando a dimensionalidade é baixa e a relação é suavizada.

4.4.4. Explicabilidade dos modelos

Referências diretas ao código/notebooks (explicabilidade):
- notebooks/model_comparison_colab.ipynb — célula 15: variável expl (estimador escolhido) e geração de SHAP (imagem shap_summary.png) ou fallback via feature_importances_ (feature_importance.png), salvos em reports/2025‑08‑15/plots/model_comparison/.
Para interpretação global e apoio à tomada de decisão, adotamos:
- Importância de features (Random Forest): reports/2025‑08‑15/plots/presentation/slides/explain_feature_importance_rf_v1.png e tabela top_importances_*.csv evidenciam os principais preditores. Em linha com o negócio, variáveis de preço/desconto, canal/PDV e sinais geográficos (UF/região) figuram entre os maiores contribuidores.
- SHAP (quando executado no Colab): reports/2025‑08‑15/plots/model_comparison/shap_summary.png oferece visão consistente de impacto marginal por feature, preservando a direção dos efeitos (positivo/negativo) e a heterogeneidade entre observações.
Essas análises reforçam a transparência do modelo recomendado, atendendo à necessidade de justificativas claras para stakeholders (ver também reports/2025‑08‑15/docs/explicabilidade_executiva.md).

4.4.5. Conclusão da comparação e recomendação
- Modelo recomendado: Random Forest Regressor, com hiperparâmetros otimizados (vide algorithm_ranking_tuned.csv e model_recommendations_tuned.csv após execução do notebook Colab).
- Justificativa: melhor compromisso entre acurácia (menor RMSE, maior R²), robustez em dados tabulares heterogêneos e interpretabilidade prática via importâncias/SHAP. Além disso, estabilidade em validação cruzada 5‑fold e baixo risco de overfitting quando comparado a alternativas mais sensíveis a tuning fino.
- Observação: manter LinReg como baseline de monitoramento e SVR como alternativa em cenários de baixa dimensionalidade altamente suavizada.

### 4.4.7 Implementação Sistemática Expandida

**Notebook Comprehensive:** `notebooks/model_comparison_comprehensive_colab.ipynb`

Para atender aos requisitos de sprint de comparação sistemática de modelos, foi desenvolvido um notebook abrangente que implementa uma metodologia rigorosa de comparação de algoritmos de machine learning. Esta implementação expandida complementa a análise baseline existente com:

**Características Técnicas Avançadas:**
- **Quatro algoritmos candidatos**: Linear Regression, Random Forest, SVR e Gradient Boosting
- **Otimização sistemática**: Grid Search para espaços pequenos, Random Search (20 iterações) para espaços maiores
- **Validação robusta**: Cross-validation 5-fold com return_train_score=True para detecção de overfitting
- **Explicabilidade**: SHAP values quando disponível, feature importance nativa como fallback
- **Compatibilidade Colab**: Detecção automática de ambiente e configuração de paths
- **Tratamento robusto**: Extensive error handling e fallbacks para dependências opcionais

**Critérios de Sucesso Quantitativos:**
- RMSE < 10% da média do target (aceitável para produção)
- MAE < 5% da média do target (comunicação confiável)
- R² > 0.8 (capacidade explicativa adequada)

**Artefatos Gerados:**
- `baseline_comparison.png`: Comparação visual dos modelos baseline
- `baseline_vs_optimized_comparison.png`: Comparação antes/depois da otimização
- `feature_importance_[modelo].png`: Análise de explicabilidade por modelo
- `shap_summary_[modelo].png`: SHAP values quando disponível
- `baseline_results.csv`: Métricas detalhadas baseline
- `baseline_vs_optimized_comparison.csv`: Comparação completa
- `model_recommendation_final.json`: Recomendação estruturada com justificativa

**Metodologia de Ranking:**
O notebook implementa um sistema de ranking baseado em score composto que considera:
- Performance (RMSE + penalização por baixo R²)
- Atendimento aos critérios de negócio (3 critérios quantitativos)
- Estabilidade entre folds (desvio padrão das métricas)

**Integração com Estrutura Existente:**
- Compatível com políticas anti-ID (SAFE_DIM_OF, BUSINESS_ENTITY_DIM)
- Utiliza estrutura de diretórios padrão (`reports/2025-08-15/`)
- Mantém consistência com documentação em português
- Preserva padrões de error handling do projeto

Referências essenciais: HASTIE; TIBSHIRANI; FRIEDMAN (2009). The Elements of Statistical Learning. Springer. PEDREGOSA et al. (2011). Scikit‑learn: Machine Learning in Python. JMLR. LUNDBERG; LEE (2017). A Unified Approach to Interpreting Model Predictions. NIPS.
4.4.6. Documentação técnica dos notebooks
A seguir, descrevemos a documentação técnica de cada notebook, incluindo objetivo, variáveis principais, métodos, células‑chave, artefatos e dependências.

A) notebooks/model_comparison_comprehensive_colab.ipynb (implementação principal expandida)
- **Objetivo e escopo**: Comparação sistemática de 4 algoritmos com otimização de hiperparâmetros, análise de explicabilidade e critérios quantitativos de sucesso. Compatível com Google Colab e ambiente local.
- **Principais variáveis/estruturas**: X_scaled (features preprocessadas), y (target), cv_results (baseline), optimized_cv_results (pós-tuning), models (candidatos), optimized_models (otimizados), comparison_df (comparação final).
- **Métodos/algoritmos**: LinearRegression, RandomForestRegressor, SVR, GradientBoostingRegressor; cross_validate com return_train_score=True; GridSearchCV; RandomizedSearchCV; SHAP TreeExplainer/Explainer; StandardScaler; LabelEncoder.
- **Seções principais**:
  • Configuração anti-ID e detecção de ambiente (Colab/local)
  • Justificativa de métricas baseada em critérios de negócio
  • Avaliação baseline com detecção de overfitting
  • Otimização sistemática de hiperparâmetros (Grid/Random Search)
  • Comparação visual baseline vs otimizado
  • Análise de explicabilidade (SHAP + feature importance)
  • Ranking final com score composto e recomendação estruturada
- **Artefatos**: baseline_comparison.png; baseline_vs_optimized_comparison.png; feature_importance_[modelo].png; shap_summary_[modelo].png; baseline_results.csv; baseline_vs_optimized_comparison.csv; feature_importance_[modelo].csv; model_recommendation_final.json.
- **Dependências/pré‑requisitos**: pandas, numpy, scikit‑learn, matplotlib, seaborn, shap (opcional com fallback); dados em data/clean/cleaned_featured.csv (com fallback para dados sintéticos).

B) notebooks/model_comparison_colab.ipynb (implementação original)
- Objetivo e escopo: comparar algoritmos, realizar tuning (Grid/Random Search), avaliar por CV 5‑fold e gerar explicabilidade (SHAP/importâncias).
- Principais variáveis/estruturas: X (features numéricas), y (alvo), res_df (baseline), best_df (tuned), best_estimators (estimadores vencedores), expl (modelo para explicabilidade).
- Métodos/algoritmos: LinearRegression, RandomForestRegressor/Classifier, SVR/SVC, LogisticRegression; cross_validate; GridSearchCV; RandomizedSearchCV; SHAP TreeExplainer (quando aplicável).
- Células‑chave: célula 3–4 (carregamento de dados), célula 6 (definição de modelos e métricas), célula 9 (CV baseline; res_df), célula 11 (tuning; best_df), célula 12 (visualizações), célula 15 (explicabilidade), célula 16 (recomendações).
- Artefatos: reports/2025‑08‑15/tables/algorithm_ranking_baseline.csv; reports/2025‑08‑15/tables/algorithm_ranking_tuned.csv; reports/2025‑08‑15/tables/model_recommendations_tuned.csv; reports/2025‑08‑15/plots/model_comparison/bar_rmse_tuned.png (ou bar_f1_tuned.png); reports/2025‑08‑15/plots/model_comparison/shap_summary.png (quando gerado); reports/2025‑08‑15/plots/model_comparison/feature_importance.png (fallback).
- Dependências/pré‑requisitos: pandas, numpy, scikit‑learn, matplotlib, seaborn, shap (opcional); dados em data/processed/features_engineered_regional.csv (ou features_engineered.csv).

C) notebooks/01_data_exploration_analysis.ipynb
- Objetivo e escopo: análise exploratória inicial das variáveis, distribuição temporal, correlações e heterogeneidades geográficas/por PDV.
- Principais variáveis/estruturas: df (dataset carregado), descrições estatísticas (describe), correlações (corr), amostragens para visualização.
- Métodos/algoritmos: estatística descritiva, heatmaps de correlação (Pearson/Spearman), histogramas/boxplots, séries temporais sintéticas.
- Células‑chave: carregamento e limpeza leve (células iniciais), geração de correlação (células intermediárias), gráficos EDA (células finais).
- Artefatos: reports/2025‑08‑15/plots/eda/* e reports/2025‑08‑15/plots/heatmap_corr.png.
- Dependências/pré‑requisitos: pandas, numpy, matplotlib, seaborn; dados em data/processed/cleaned.parquet ou CSV equivalente.

D) notebooks/02_feature_engineering.ipynb
- Objetivo e escopo: preparação e transformação de features; checagens de normalidade/outliers; split supervisionado 60/20/20.
- Principais variáveis/estruturas: tabelas de auditoria (normality_tests.csv; outlier_detection_rates.csv), dicionário de transformações (transformation_pipeline.json), splits (train_set.csv, validation_set.csv, test_set.csv).
- Métodos/algoritmos: transformações (log1p/Box‑Cox), escalonamento (z‑score, robusto), seleção por colinearidade; exportação de splits estratificados.
- Células‑chave: diagnóstico de distribuição, montagem do pipeline de transformação, geração de splits e persistência.
- Artefatos: reports/2025‑08‑15/tables/feature_engineering_summary.csv; reports/2025‑08‑15/tables/normality_tests.csv; reports/2025‑08‑15/tables/transformation_pipeline.json; data/processed/train_set.csv, validation_set.csv, test_set.csv.
- Dependências/pré‑requisitos: pandas, numpy, scipy, scikit‑learn, seaborn, matplotlib; dataset base em data/processed/features_cleaned_pre_territorial*.csv.

E) notebooks/03_model_development_comparison.ipynb
- Objetivo e escopo: comparação baseline de algoritmos com CV, sem tuning extensivo (contraparte local do Colab).
- Principais variáveis/estruturas: res_df (tabela de métricas por algoritmo), possivelmente X, y derivados do dataset processado.
- Métodos/algoritmos: cross_validate com scoring apropriado; modelos lineares, ensemble e margens máximas.
- Células‑chave: célula de validação cruzada responsável por alimentar reports/2025‑08‑15/tables/algorithm_ranking.csv (variável res_df).
- Artefatos: reports/2025‑08‑15/tables/algorithm_ranking.csv; reports/2025‑08‑15/plots/model_comparison/bar_rmse.png.
- Dependências/pré‑requisitos: pandas, numpy, scikit‑learn, seaborn, matplotlib.

E) notebooks/04_territorial_analysis.ipynb
- Objetivo e escopo: análise territorial supervisionada e não supervisionada; mapas e sumários por UF/região; recomendações executivas.
- Principais variáveis/estruturas: cluster_assignments.csv; cluster_territorial_geography_summary.csv; mapas HTML/PNG.
- Métodos/algoritmos: K‑Means (seleção de K por Silhouette), Random Forest para potenciais regionais (quando aplicável), geração de mapas (folium/plotly).
- Células‑chave: preparação de dados territoriais, treinamento/avaliação de clusters, renderização de mapas.
- Artefatos: reports/2025‑08‑15/plots/territorial_clustering_map*.html|png; reports/2025‑08‑15/tables/cluster_assignments*.csv; reports/2025‑08‑15/tables/cluster_territorial_geography_summary.csv.
- Dependências/pré‑requisitos: pandas, numpy, scikit‑learn, seaborn, matplotlib, folium/plotly; dados com UF propagada (features_engineered_regional.csv).

F) notebooks/05_customer_segmentation.ipynb
- Objetivo e escopo: segmentação de clientes para campanhas e perfilização.
- Principais variáveis/estruturas: matriz de features de clientes (numérica), labels de cluster (quando gerados).
- Métodos/algoritmos: K‑Means ou algoritmos equivalentes; avaliação por Silhouette; PCA para inspeção.
- Células‑chave: engenharia de variáveis de cliente, ajuste de K, interpretação de clusters.
- Artefatos: tabelas de segmentos e gráficos de clusterização (quando executado).
- Dependências/pré‑requisitos: pandas, numpy, scikit‑learn, seaborn, matplotlib.

G) notebooks/06_business_insights_reporting.ipynb
- Objetivo e escopo: consolidação de insights, painéis e materiais executivos.
- Principais variáveis/estruturas: tabelas de métricas finais (ex.: supervised_by_cluster_metrics*.csv), gráficos de desempenho e de clusters.
- Métodos/algoritmos: apenas leitura e visualização dos artefatos consolidados; sem treinamento adicional.
- Células‑chave: carga dos artefatos de reports/2025‑08‑15, curadoria de gráficos e sumários.
- Artefatos: reports/2025‑08‑15/plots/presentation/*; reports/2025‑08‑15/docs/*.
- Dependências/pré‑requisitos: pandas, numpy, seaborn, matplotlib (e folium/plotly se mapas forem embutidos).

### 4.5 Avaliação

```
- Descreva a solução final de modelo preditivo e justifique a escolha. Alinhe sua justificativa com a Seção 4.1, resgatando o entendimento
  do negócio e das personas, explicando de que formas seu modelo atende os requisitos e definições.
- Descreva também um plano de contingência para os casos em que o modelo falhar em suas predições.
- Além disso, discuta sobre a explicabilidade do modelo (se aplicável) e realize a verificação de aceitação ou refutação das hipóteses.
- Se aplicável, utilize equações, tabelas e gráficos de visualização de dados para melhor ilustrar seus argumentos.

```

## <a name="c5"></a>5. Conclusões e Recomendações

```
Escreva, de forma resumida, sobre os principais resultados do seu projeto e faça recomendações formais ao seu parceiro de negócios em relação ao uso desse modelo. Você pode aproveitar este espaço para comentar sobre possíveis materiais extras, como um manual de usuário mais detalhado na seção “Anexos”. Não se esqueça também das pessoas que serão potencialmente afetadas pelas decisões do modelo preditivo e elabore recomendações que ajudem seu parceiro a tratá-las de maneira estratégica e ética.

Remova este bloco ao final
```

## <a name="c6"></a>6. Referências

## <a name="attachments"></a>Anexos

&nbsp;&nbsp;Nesta etapa, o objetivo é formular hipóteses estatísticas relacionadas à normalidade das variáveis quantitativas da base de dados da Chilli Beans. A definição dessas hipóteses é essencial, pois orienta a aplicação dos testes estatísticos e garante critérios claros para a interpretação dos resultados.

- **Hipótese Nula (H₀):** A variável segue uma distribuição normal.
- **Hipótese Alternativa (H₁):** A variável não segue uma distribuição normal.

&nbsp;&nbsp;Para este projeto, foi adotado o nível de significância de α = 0,05 (5%), valor amplamente utilizado em análises estatísticas. A regra de decisão é definida da seguinte forma:
- Se p-valor > 0,05 → não rejeitamos H₀, ou seja, a variável pode ser considerada como normalmente distribuída.
- Se p-valor ≤ 0,05 → rejeitamos H₀, aceitando H₁, o que indica que a variável não segue uma distribuição normal.

**Nota metodológica:** Considera-se que as observações são independentes e que as variáveis numéricas são medidas em escala contínua (ou aproximadamente contínua).

---

### Implementação do teste de normalidade (Kolmogorov–Smirnov)

&nbsp;&nbsp;Com as hipóteses definidas, procedeu-se à aplicação do **teste de Kolmogorov–Smirnov (KS)**. Optou-se por este teste porque ele é adequado para amostras grandes, como a da base analisada (mais de 40 mil registros), e permite verificar se a distribuição empírica dos dados se distancia significativamente de uma distribuição normal teórica. Além disso, o KS é sensível a diferenças tanto na forma quanto na posição da distribuição, oferecendo uma avaliação robusta da normalidade dos dados após padronização (média 0 e desvio-padrão 1).


**Variáveis analisadas:**
- **Valor_Total**
- **Desconto**
- **Total_Preco_Liquido**

Clique [aqui](https://colab.research.google.com/drive/1kieMROx7CcsH19Jn7TyMYkVm3MSaqYgI?usp=sharing) para acessar o notebook
---

### Resultados obtidos

**Tabela 1 – Resultados do teste de Kolmogorov–Smirnov**

| Variável             | KS (estatística) | p-valor | Média     | Mediana  | Conclusão |
|----------------------|------------------|---------|-----------|----------|-----------|
| Valor_Total          | 0,173            | 0,000   | 267,30    | 299,98   | Rejeita H₀ (não normal) |
| Desconto             | 0,435            | 0,000   | 19,15     | 0,00     | Rejeita H₀ (não normal) |
| Total_Preco_Liquido  | 0,213            | 0,000   | 290,49    | 299,98   | Rejeita H₀ (não normal) |

&nbsp;&nbsp;Observa-se que todas as variáveis apresentam forte evidência contra a normalidade (p-valores = 0,000).

---

### Histogramas das variáveis

Para cada variável, foi construído um histograma. As linhas verticais indicam **média** (— —) e **mediana** (⋯).

**Resumo visual:**
- **Valor_Total** → cauda à direita e assimetria → não normal.
- **Desconto** → concentração em zero (muitos registros sem desconto) e cauda longa → não normal.
- **Total_Preco_Liquido** → comportamento semelhante ao Valor_Total, com assimetria à direita → não normal.

<div align="center">

<sub>Figura x: Histograma de Valor_Total</sub>

<img src="/assets/hist_Valor_Total.png" style=width:100%>

<sub>Fonte: Autoria própria (2025)</sub>

</div>

&nbsp;&nbsp;**Valor_Total:** O histograma apresenta concentração em valores baixos e uma cauda longa à direita. A média ficou maior que a mediana, evidenciando assimetria positiva. Isso reforça o resultado do teste KS, que rejeitou a hipótese de normalidade.

<div align="center">

<sub>Figura x: Histograma de Total_Preço_Líquido</sub>

<img src="/assets/hist_Total_Preco_Liquido.png" style=width:100%>

<sub>Fonte: Autoria própria (2025)</sub>

</div>

&nbsp;&nbsp;**Total_Preco_Liquido:** Assim como no Valor_Total, há cauda à direita e média menor que a mediana, indicando assimetria positiva. A forma do histograma confirma que a variável não segue uma distribuição normal.

<div align="center">

<sub>Figura x: Histograma de Desconto</sub>

<img src="/assets/hist_Desconto.png" style=width:100%>

<sub>Fonte: Autoria própria (2025)</sub>

</div>

&nbsp;&nbsp;**Desconto:** Observa-se forte concentração em zero (muitos registros sem desconto) e uma cauda longa em valores positivos. A mediana é zero, enquanto a média é mais alta, mostrando uma distribuição zero-inflada e assimétrica. Esse padrão confirma a rejeição da normalidade.

---

### Comparação entre média e mediana

**Tabela 2 – Média vs. Mediana**

| Variável             | Média     | Mediana  | Interpretação |
|----------------------|-----------|----------|---------------|
| Valor_Total          | 267,30    | 299,98   | Mediana maior que a média → assimetria à direita. |
| Desconto             | 19,15     | 0,00     | Forte discrepância (mediana zero e média positiva) → distribuição zero-inflada. |
| Total_Preco_Liquido  | 290,49    | 299,98   | Mediana > média → assimetria à direita. |

**Conclusão:** todas as variáveis rejeitam a hipótese de normalidade. Os resultados são consistentes entre o teste estatístico, os histogramas e a comparação de média/mediana.


## Escalonamento nas Variáveis Quantitativas

### a) Tipo de escalonamento utilizado e justificativa

Para as três variáveis analisadas – `Valor_Total`, `Desconto` e `Total_Preco_Liquido` – o método de escalonamento escolhido foi a **Normalização (Min-Max Scaler)**.

A **Padronização (Standard Scaler)** é recomendada quando os dados seguem uma distribuição aproximadamente normal, pois utiliza a média e o desvio padrão. Contudo, conforme demonstrado na análise de normalidade, todas as variáveis rejeitaram a hipótese de normalidade:

- **Valor_Total:** Assimétrica à direita, com cauda longa.
- **Desconto:** Zero-inflada, concentrada em 0 com cauda longa positiva.
- **Total_Preco_Liquido:** Assimétrica à direita, semelhante ao Valor_Total.

Diante disso, a Normalização é mais apropriada, pois não depende da normalidade e apenas ajusta os valores para o intervalo [0, 1].

---

### b) Estatísticas utilizadas para os cálculos

Para a **Normalização (Min-Max)** utilizam-se os valores **mínimo** e **máximo** de cada variável.

**Tabela 3 – Estatísticas descritivas (valores reais da base)**

| Variável             | Média   | Desvio Padrão | Mínimo   | Máximo    |
|----------------------|---------|---------------|----------|-----------|
| Valor_Total          | 267,30  | —             | -400,00  | 209.700,00 |
| Desconto             | 19,15   | —             | 0,00     | 1.849,97  |
| Total_Preco_Liquido  | 290,49  | —             | -439,98  | 209.700,00 |

*(Obs.: valores negativos estão associados a devoluções de mercadorias.)*

---

### c) Equações aplicadas (com constantes substituídas)

A fórmula da **Normalização Min-Max** é:

> X_normalizado = (X_original – X_mín) / (X_máx – X_mín)

Aplicando aos valores da Tabela 3:

1. **Valor_Total:**
   > (Valor_Total – (-400,00)) / (209.700 – (-400,00))

2. **Desconto:**
   > (Desconto – 0,00) / (1.849,97 – 0,00) = Desconto / 1.849,97

3. **Total_Preco_Liquido:**
   > (Total_Preco_Liquido – (-439,98)) / (209.700 – (-439,98))

---

### d) Histogramas após o escalonamento

O processo de escalonamento não altera a forma das distribuições, apenas a escala no eixo X.

- **Valor_Total:** mantém a cauda à direita.
- **Desconto:** continua concentrado em 0 e com cauda longa.
- **Total_Preco_Liquido:** permanece assimétrico à direita.

<div align="center">

<sub>Figura x: Histograma de Valor_Total_Normalizado</sub>

<img src="/assets/hist_Valor_Total_norm_norm.png" style=width:100%>

<sub>Fonte: Autoria própria (2025)</sub>

</div>

<div align="center">

<sub>Figura x: Histograma de Total_Preço_Líquido_Normalizado</sub>

<img src="/assets/hist_Total_Preco_Liquido_norm_norm.png" style=width:100%>

<sub>Fonte: Autoria própria (2025)</sub>

</div>

<div align="center">

<sub>Figura x: Histograma de Desconto</sub>

<img src="/assets/hist_Desconto_norm_norm.png" style=width:100%>

<sub>Fonte: Autoria própria (2025)</sub>

</div>


Portanto, os histogramas pós-normalização têm o mesmo formato dos originais, mas agora todos os valores estão no intervalo [0, 1].

---

### e) Comparação entre dados originais e escalonados

**Tabela 4 – 10 primeiros registros originais**

| Registro | Valor_Total | Desconto | Total_Preco_Liquido |
|----------|-------------|----------|---------------------|
| 1        | 26,30       | 20,34    | 26,98               |
| 2        | 389,90      | 20,34    | 399,98              |
| 3        | 26,01       | 30,96    | 26,98               |
| 4        | 58,68       | 9,96     | 59,98               |
| 5        | -400,00     | 39,98    | -439,98             |
| 6        | 299,98      | 0,00     | 299,98              |
| 7        | 149,99      | 10,00    | 139,99              |
| 8        | 499,99      | 15,00    | 484,99              |
| 9        | 999,90      | 25,00    | 974,90              |
| 10       | 29,98       | 0,00     | 29,98               |

**Tabela 5 – 10 primeiros registros normalizados**

| Registro | Valor_Total_norm | Desconto_norm | Total_Preco_Liquido_norm |
|----------|------------------|---------------|--------------------------|
| 1        | 0,0019           | 0,0110        | 0,0020                   |
| 2        | 0,0038           | 0,0110        | 0,0039                   |
| 3        | 0,0019           | 0,0167        | 0,0020                   |
| 4        | 0,0023           | 0,0054        | 0,0023                   |
| 5        | 0,0000           | 0,0216        | 0,0000                   |
| 6        | 0,0033           | 0,0000        | 0,0033                   |
| 7        | 0,0022           | 0,0054        | 0,0021                   |
| 8        | 0,0043           | 0,0081        | 0,0042                   |
| 9        | 0,0080           | 0,0135        | 0,0079                   |
| 10       | 0,0021           | 0,0000        | 0,0021                   |

*(valores exemplificativos, mantêm a escala [0,1])*

---

### Conclusão

---

A análise realizada nas variáveis quantitativas qtd, valor e Desconto foi um processo fundamental de duas etapas, que permitiu compreender a natureza estatística dos dados e prepará-los adequadamente para a aplicação de modelos preditivos.

A primeira etapa, focada nos testes de hipótese e análise de normalidade, foi conclusiva em demonstrar que nenhuma das variáveis adere a uma distribuição normal. Através de testes estatísticos formais, análise de histogramas e a comparação entre média e mediana, identificou-se um forte padrão de assimetria à direita em todas as variáveis, com a particularidade da variável Desconto ser zero-inflada (com alta concentração no valor zero).

Essa constatação de não normalidade foi o pilar que guiou as decisões na etapa seguinte. Para o escalonamento dos dados, optou-se estrategicamente pela Normalização (Min-Max Scaler). Essa escolha foi justificada pelo fato de a Normalização não pressupor uma distribuição específica para os dados, sendo mais robusta à presença de outliers e à forte assimetria que invalidariam a eficácia da Padronização (que depende da média e do desvio padrão).

Ao final do processo, todas as variáveis foram transformadas com sucesso para um intervalo comum de 0 a 1, preservando a forma original de suas distribuições. Em suma, o trabalho cumpriu seus objetivos ao realizar um diagnóstico estatístico preciso que, por sua vez, informou a aplicação de uma técnica de pré-processamento coerente e justificada. O conjunto de dados está agora mais robusto e preparado, com suas principais variáveis quantitativas devidamente escalonadas e prontas para serem utilizadas de forma eficaz nas próximas etapas do projeto.


### 4.3. Resultados e Discussão dos Modelos

Nesta subseção, apresentam-se os resultados consolidados da preparação de dados (Feature Engineering) e da comparação de algoritmos. Os artefatos referenciados foram gerados pelos notebooks notebooks/chilli_beans_analysis.ipynb, notebooks/feature_engineering.ipynb e notebooks/model_comparison.ipynb e encontram-se sob reports/2025-08-15/.

#### 4.3.1 Organização dos dados (treino/validação/teste)

A divisão supervisionada 60/20/20 com estratificação foi habilitada no notebook de Feature Engineering (SUPERVISED=True; TARGET_COL='valor'). Assim, os conjuntos foram gerados e versionados, e a distribuição do alvo por split foi documentada:

- Arquivos: data/processed/train_set.csv; validation_set.csv; test_set.csv
- Variável-alvo: valor (regressão)
- Distribuição do alvo por split: reports/2025-08-15/tables/target_distribution_by_split.csv

Resumo (amostra dos resultados atuais):
- train: n=21369; média≈27354,80; p50=29998,0; p75=39998,0
- validation: n=7123; média≈27714,97; p50=29998,0; p75=39998,0
- test: n=7124; média≈27137,30; p50=29998,0; p75=39998,0

Estratégia de estratificação: quando a cardinalidade do alvo permite, realiza-se estratificação por valor discretizado (binning implícito via API quando aplicável). Caso contrário, aplica-se split aleatório com semente fixa (42) garantindo reprodutibilidade.

Validação final: após seleção do melhor modelo por validação cruzada em treino/validação, reportamos métricas finais no conjunto de teste independente, evitando vazamento de dados.

#### 4.3.2 Modelagem e Features

A seleção e transformação das variáveis seguiram os achados da EDA e foram materializadas em:
- Sumário de features e justificativas: reports/2025-08-15/tables/feature_engineering_summary.csv
- Pipeline reproduzível (configuração): reports/2025-08-15/tables/transformation_pipeline.json

Principais decisões e justificativas (exemplos representativos):
- Normalização/padronização guiada por testes de normalidade e robustez; variáveis com p-valor de Shapiro > 0,05 receberam padronização z-score; variáveis não-normais, escalonamento robusto (mediana/MAD); min-max aplicado em casos específicos de interpretabilidade.
- Outliers tratados conforme recomendações em reports/2025-08-15/tables/preprocessing_config.json (métodos sigma ±3σ ou IQR ±1,5 IQR). A winsorização foi preferida à remoção para preservar amostra e reduzir sensibilidade a extremos.
- Transformações de distribuição para redução de assimetria:
  - valor: Box-Cox (λ ≈ 0,592)
  - ID_Faturamento: log1p
  - id_loja: Box-Cox (λ ≈ 0,866)
  - ID_Cliente: Box-Cox (λ ≈ 2,884)
  - Exemplo adicional (conforme sumário): Preco_Custo: log1p
- Features derivadas: inclusão de razões simples (e.g., reasonáveis entre variáveis escaladas com |corr| > 0,7), termos quadráticos de até três variáveis e atributos temporais (dow, month) quando presentes.

Figura de verificação pós‑transformações:
- Matriz de correlação final: reports/2025-08-15/plots/feature_engineering/final_corr_heatmap.png

#### 4.3.3 Métricas dos modelos (validação cruzada 5-fold)

A comparação de algoritmos (tarefa de regressão) está documentada em reports/2025-08-15/tables/algorithm_ranking.csv. A seguir, sintetizamos as principais métricas (média ± desvio‑padrão por 5 folds):

- Random Forest (RF):
  - RMSE = 0,0199 ± 0,0088
  - MAE = 0,00159 ± 0,00032
  - R² = 0,9986 ± 0,0011
- Gradient Boosting (GB, implementação baseline via RandomForestRegressor expandido):
  - RMSE = 0,0201 ± 0,0083
  - MAE = 0,00160 ± 0,00030
  - R² = 0,9986 ± 0,0011
- SVM (RBF, com padronização):
  - RMSE = 0,1245 ± 0,0115
  - MAE = 0,0729 ± 0,00117
  - R² = 0,9551 ± 0,0077
- Regressão Linear:
  - RMSE = 14,9510 ± 18,1120
  - MAE = 0,5970 ± 0,6987
  - R² = −1601,7553 ± 1971,5856
- MLPRegressor:
  - RMSE = 1,2459 ± 0,6728
  - MAE = 0,3037 ± 0,1293
  - R² = −4,7707 ± 5,7210

Observação: os valores de desvio‑padrão são reportados conforme arquivo de ranking; apesar de a métrica RMSE/MAE derivar de escores “neg_*” durante o cálculo, o ranking final já está em escala positiva para as médias, mantendo consistência de comparação entre modelos.

#### 4.3.4 Primeiro modelo candidato e discussão

Com base no resumo executivo reports/2025-08-15/tables/model_recommendations.csv, o algoritmo recomendado para a tarefa de regressão foi:

- Modelo recomendado: Random Forest (RF)
- Critério: menor erro (RMSE médio)
- Valor observado: RMSE médio ≈ 0,0199

Análise e trade‑offs:
- Desempenho: RF e o baseline “GB” (RF com maior ensemble) apresentaram desempenho superior e estável (R² ≈ 0,999), indicando excelente capacidade de ajuste sob as features disponíveis.
- Interpretabilidade: há perda relativa frente a modelos lineares; recomenda‑se complementar com importâncias de features (tree‑based), SHAP e/ou PDPs para explicabilidade.
- Custo computacional: maior que LinReg, compatível com SVM/MLP em cenários médios; tempo de treinamento é aceitável para o tamanho atual do dataset.
- Robustez: ensembles costumam lidar bem com não‑linearidades e outliers residuais, coerente com a distribuição pós‑tratamentos aplicada.

Próximos passos de otimização:
- Ajuste fino de hiperparâmetros via busca Bayesiana ou Random Search (n_estimators, max_depth, max_features, min_samples_leaf)
- Seleção parcimoniosa de variáveis para reduzir potencial redundância e custo de inferência
- Complemento de visualizações: curvas de aprendizado, gráfico de tempo vs. performance, predito vs. real e análise de resíduos

Referências visuais adicionais (EDA e qualidade dos dados):
- Heatmaps de correlação (Pearson, Spearman, Kendall):
  - reports/2025-08-15/plots/eda/heatmap_corr_pearson.png
  - reports/2025-08-15/plots/eda/heatmap_corr_spearman.png
  - reports/2025-08-15/plots/eda/heatmap_corr_kendall.png
- Outliers e normalidade (Q‑Q plots, histogramas vs. normal, elipses bivariadas, filtros comparativos):
  - reports/2025-08-15/plots/eda/outliers/qqplot_*.png
  - reports/2025-08-15/plots/eda/outliers/hist_normal_*.png
  - reports/2025-08-15/plots/eda/outliers/scatter_ellipses.png
  - reports/2025-08-15/plots/eda/outliers/filters_compare_*.png

Conforme as diretrizes metodológicas (Seção 3), a presente documentação consolida evidências quantitativas (métricas e testes) e qualitativas (visualizações), assegurando reprodutibilidade por meio do pipeline salvo (reports/2025-08-15/tables/transformation_pipeline.json) e dos artefatos de preparação (feature_engineering_summary.csv). Em iterações subsequentes, recomenda‑se ativar o split supervisionado 60/20/20 e expandir as análises de explicabilidade do modelo recomendado para suporte à decisão executiva.
